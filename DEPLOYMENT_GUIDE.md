
# دليل النشر والتشغيل - نظام متابعة الوارد والصادر (IOTS)
## Deployment and Operation Guide - In and Out Tracking System

---

## 📋 جدول المحتويات

1. [متطلبات النظام](#متطلبات-النظام)
2. [التثبيت السريع](#التثبيت-السريع)
3. [الإعداد المفصل](#الإعداد-المفصل)
4. [التشغيل](#التشغيل)
5. [الاختبار](#الاختبار)
6. [الصيانة](#الصيانة)
7. [استكشاف الأخطاء](#استكشاف-الأخطاء)

---

## 🖥️ متطلبات النظام

### الخادم (Server)
- **نظام التشغيل:** Windows 10/11, Ubuntu 20.04+, CentOS 8+
- **Python:** 3.8 أو أحدث
- **MySQL:** 5.7 أو أحدث (اختياري)
- **ذاكرة الوصول العشوائي:** 4GB كحد أدنى، 8GB مُوصى به
- **مساحة القرص الصلب:** 2GB كحد أدنى، 10GB مُوصى به
- **الشبكة:** اتصال إنترنت للتحديثات

### العميل (Client)
- **نظام التشغيل:** Windows 10/11
- **ذاكرة الوصول العشوائي:** 2GB كحد أدنى
- **مساحة القرص الصلب:** 1GB
- **متصفح الويب:** Chrome 90+, Firefox 88+, Edge 90+

---

## ⚡ التثبيت السريع

### الطريقة الأولى: الإعداد التلقائي

```bash
# 1. تحميل المشروع
git clone https://github.com/your-repo/iots.git
cd iots

# 2. تشغيل الإعداد التلقائي
python setup_system.py
```

### الطريقة الثانية: الإعداد اليدوي

```bash
# 1. إنشاء بيئة افتراضية
python -m venv venv

# 2. تفعيل البيئة الافتراضية
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

# 3. تثبيت المتطلبات
pip install -r requirements.txt

# 4. تحميل الأصول
python download_assets.py

# 5. إعداد قاعدة البيانات
python -c "from config.database import setup_database; setup_database()"
```

---

## 🔧 الإعداد المفصل

### 1. إعداد قاعدة البيانات

#### MySQL (وصى به للإنتاج)

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE iots_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم
CREATE USER 'iots_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON iots_db.* TO 'iots_user'@'localhost';
FLUSH PRIVILEGES;
```

#### تحديث إعدادات الاتصال

عدّل ملف `config/configuration.ini`:

```ini
[MYSQL]
host = localhost
port = 3306
database = iots_db
user = iots_user
password = secure_password
charset = utf8mb4
collation = utf8mb4_unicode_ci
```

### 2. إعداد الأمان

#### تغيير المفتاح السري

عدّل في `web_app/app.py`:

```python
app.config['SECRET_KEY'] = 'your-very-secure-secret-key-here'
```

#### تحديث كلمات المرور الافتراضية

```python
# تشغيل سكريبت تحديث كلمات المرور
python -c "
from models.user import UserManager
um = UserManager()
um.update_user_password('admin', 'new_secure_password')
"
```

### 3. إعداد الخادم

#### للتطوير (Development)

```bash
python main.py
```

#### للإنتاج (Production) مع Gunicorn

```bash
# تثبيت Gunicorn
pip install gunicorn

# تشغيل الخادم
gunicorn -w 4 -b 0.0.0.0:5000 "web_app.app:create_app()"
```

#### إعداد Nginx (اختياري)

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location /static {
        alias /path/to/iots/web_app/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

---

## 🚀 التشغيل

### تطبيق الويب

```bash
# الطريقة الأولى: الملف الرئيسي
python main.py

# الطريقة الثانية: مباشرة
python web_app/app.py

# الطريقة الثالثة: الاختصار (Windows)
تشغيل_تطبيق_الويب.bat
```

**الوصول:** http://localhost:5000

### تطبيق سطح المكتب

```bash
# الطريقة الأولى: من الملف الرئيسي
python main.py
# ثم اختر الخيار 2

# الطريقة الثانية: مباشرة
python desktop_app/main.py

# الطريقة الثالثة: الاختصار (Windows)
تشغيل_تطبيق_سطح_المكتب.bat
```

### بيانات تسجيل الدخول الافتراضية

| المستخدم | كلمة المرور | الصلاحية |
|----------|-------------|----------|
| admin    | admin123    | مدير     |
| user1    | user123     | مستخدم   |
| user2    | user123     | مستخدم   |

---

## 🧪 الاختبار

### اختبار شامل للنظام

```bash
python test_system.py
```

### اختبار مكونات محددة

```bash
# اختبار قاعدة البيانات
python -c "from config.database import DatabaseConnection; db = DatabaseConnection(); db.test_connection()"

# اختبار تطبيق الويب
python -c "from web_app.app import create_app; app = create_app(); print('Web app OK')"

# اختبار نظام التقارير
python -c "from utils.reports import test_reports; test_reports()"
```

---

## 🔧 الصيانة

### تحسين الأداء

```bash
# تحسين شامل
python optimize_system.py

# أو تشغيل تفاعلي
python optimize_system.py
```

### النسخ الاحتياطي

#### قاعدة بيانات MySQL

```bash
mysqldump -u iots_user -p iots_db > backup_$(date +%Y%m%d_%H%M%S).sql
```

#### قاعدة بيانات SQLite

```bash
cp db/local.db backups/local_backup_$(date +%Y%m%d_%H%M%S).db
```

### تحديث النظام

```bash
# تحديث الكود
git pull origin main

# تحديث المتطلبات
pip install -r requirements.txt --upgrade

# تحديث قاعدة البيانات (إذا لزم الأمر)
python -c "from config.database import setup_database; setup_database()"
```

---

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في الاتصال بقاعدة البيانات

**المشكلة:** `فشل الاتصال بقاعدة البيانات`

**الحلول:**
```bash
# التحقق من حالة MySQL
sudo systemctl status mysql

# إعادة تشغيل MySQL
sudo systemctl restart mysql

# التحقق من إعدادات الاتصال
python -c "from config.database import DatabaseConnection; db = DatabaseConnection(); db.test_connection()"
```

#### 2. خطأ في تثبيت المتطلبات

**المشكلة:** `pip install فشل`

**الحلول:**
```bash
# تحديث pip
python -m pip install --upgrade pip

# تثبيت المتطلبات واحد تلو الآخر
pip install flask
pip install PyQt6
# إلخ...

# استخدام مرآة مختلفة
pip install -r requirements.txt -i https://pypi.douban.com/simple/
```

#### 3. مشكلة في تطبيق سطح المكتب

**المشكلة:** `PyQt6 غير متاح`

**الحلول:**
```bash
# تثبيت PyQt6
pip install PyQt6

# أو استخدام PyQt5 كبديل
pip install PyQt5
# ثم عل الاستيرادات في desktop_app/main.py
```

#### 4. مشكلة في الخطوط العربية

**المشكلة:** النصوص العربية لا تظهر بشكل صحيح

**الحلول:**
- تأكد من وجود خط Cairo في النظام
- تحديث ملف `web_app/static/css/cairo-font.css`
- استخدام خط بديل مثل Arial أو Tahoma

### سجلات الأخطاء

#### تفعيل السجلات المفصلة

```python
# في web_app/app.py
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### مواقع ملفات السجل

- **تطبيق الويب:** `logs/web_app.log`
- **تطبيق سطح المكتب:** `logs/desktop_app.log`
- **قاعدة البيانات:** `logs/database.log`

---

## 📞 الدعم الفني

### قنوات الدعم

- **البريد الإلكتروني:** <EMAIL>
- **GitHub Issues:** [رابط المشاكل](https://github.com/your-repo/iots/issues)
- **الوثائق:** [README.md](README.md)

### معلومات مفيدة للدعم

عند طلب الدعم، يرجى تضمين:

1. **إصدار النظام:** `python --version`
2. **نظام التشغيل:** Windows/Linux/macOS
3. **رسالة الخطأ الكاملة**
4. **خطوات إعادة إنتاج المشكلة**
5. **ملفات السجل ذات الصلة**

---

## 📈 مراقبة الأداء

### مؤشرات الأداء الرئيسية

- **زمن الاستجابة:** < 2 ثانية للصفحات العادية
- **استخدام الذاكرة:** < 512MB للتطبيق الواحد
- **استخدام المعالج:** < 50% في الحالة العادية
- **مساحة قاعدة البيانات:** مراقبة النمو الشهري

### أدوات المراقبة

```bash
# مراقبة استخدام الموارد
htop

# مراقبة قاعدة البيانات
mysql -u root -p -e "SHOW PROCESSLIST;"

# مراقبة مساحة القرص
df -h
```

---

## 🔄 التحديثات المستقبلية

### خارطة الطريق

- **v1.1:** نظام الإشعارات المتقدم
- **v1.2:** API RESTful كامل
- **v1.3:** تطبيق الهاتف المحمول
- **v2.0:** الذكاء الاصطناعي والتحليلات المتقدمة

### عملية التحديث

1. **النسخ الاحتياطي** لقاعدة البيانات والملفات
2. **تحميل** الإصدار الجديد
3. **تشغيل** سكريبت التحديث
4. **اختبار** الوظائف الأساسية
5. **نشر** التحديث للمستخدمين

---

**© 2024 نظام متابعة الوارد والصادر (IOTS) - جميع الحقوق محفوظة**

*آخر تحديث: يناير 2024*


