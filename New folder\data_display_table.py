from PyQt5.QtWidgets import QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QLabel
from PyQt5.QtCore import Qt

class DataDisplayTable(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        main_layout = QVBoxLayout()

        title_label = QLabel("عرض البيانات - Data View")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        main_layout.addWidget(title_label)

        self.table_widget = QTableWidget()
        main_layout.addWidget(self.table_widget)

        self.load_dummy_data()

        self.setLayout(main_layout)

    def load_dummy_data(self):
        # Dummy data for demonstration, similar to the 'users' sheet
        headers = ["User ID", "User Name", "User Pass", "Permission", "Screen"]
        data = [
            ["09998", "إبراهيم سعيد محمد علي عبده", "09998", "admin", ""],
            ["81103", "مريم محمد عبدالله حفناوى", "81103", "user", ""],
            ["52797", "بحيري ابراهيم احمد محمد ابراهيم", "52797", "user", ""],
            ["83218", "فاتن محمد احمد محمد", "83218", "user", ""],
        ]

        self.table_widget.setRowCount(len(data))
        self.table_widget.setColumnCount(len(headers))
        self.table_widget.setHorizontalHeaderLabels(headers)

        for row_idx, row_data in enumerate(data):
            for col_idx, item in enumerate(row_data):
                self.table_widget.setItem(row_idx, col_idx, QTableWidgetItem(str(item)))

        self.table_widget.resizeColumnsToContents()


