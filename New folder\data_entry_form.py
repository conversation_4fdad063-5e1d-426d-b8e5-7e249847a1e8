from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, 
                             QLineEdit, QComboBox, QDateEdit, QPushButton, QLabel, 
                             QMessageBox, QGroupBox)
from PyQt5.QtCore import QDate, Qt

class DataEntryForm(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        main_layout = QVBoxLayout()
        
        # Title
        title_label = QLabel("إدخال بيانات جديدة - New Data Entry")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        main_layout.addWidget(title_label)

        # Main Record Form
        main_group = QGroupBox("البيانات الأساسية - Main Record")
        main_form_layout = QFormLayout()

        # Head Incoming Number
        self.head_incoming_no = QLineEdit()
        self.head_incoming_no.setPlaceholderText("رقم وارد رئيس المصلحة")
        main_form_layout.addRow("Head Incoming No:", self.head_incoming_no)

        # Letter
        self.letter = QLineEdit()
        self.letter.setPlaceholderText("حرف")
        self.letter.setMaxLength(3)
        main_form_layout.addRow("Letter:", self.letter)

        # Head Incoming Date
        self.head_incoming_date = QDateEdit()
        self.head_incoming_date.setDate(QDate.currentDate())
        self.head_incoming_date.setCalendarPopup(True)
        main_form_layout.addRow("Head Incoming Date:", self.head_incoming_date)

        # Visa Type
        self.visa_type = QComboBox()
        self.visa_type.addItems(["نظر", "للحفظ", "لاتخاذ اللازم", "للدراسة والعرض", "لاخطار الجهة الشاكية"])
        main_form_layout.addRow("Visa Type:", self.visa_type)

        main_group.setLayout(main_form_layout)
        main_layout.addWidget(main_group)

        # User Information Form
        user_group = QGroupBox("معلومات المستخدم - User Information")
        user_form_layout = QFormLayout()

        # User ID
        self.user_id = QLineEdit()
        self.user_id.setPlaceholderText("معرف المستخدم")
        user_form_layout.addRow("User ID:", self.user_id)

        # User Name
        self.user_name = QLineEdit()
        self.user_name.setPlaceholderText("اسم المستخدم")
        user_form_layout.addRow("User Name:", self.user_name)

        # Permission
        self.permission = QComboBox()
        self.permission.addItems(["admin", "user"])
        user_form_layout.addRow("Permission:", self.permission)

        user_group.setLayout(user_form_layout)
        main_layout.addWidget(user_group)

        # Buttons
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ - Save")
        self.save_button.clicked.connect(self.save_data)
        self.save_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 8px; }")
        
        self.clear_button = QPushButton("مسح - Clear")
        self.clear_button.clicked.connect(self.clear_form)
        self.clear_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; padding: 8px; }")
        
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.clear_button)
        
        main_layout.addLayout(button_layout)
        
        self.setLayout(main_layout)

    def save_data(self):
        # Placeholder for save functionality
        QMessageBox.information(self, "حفظ - Save", "تم حفظ البيانات بنجاح!\nData saved successfully!")

    def clear_form(self):
        # Clear all form fields
        self.head_incoming_no.clear()
        self.letter.clear()
        self.head_incoming_date.setDate(QDate.currentDate())
        self.visa_type.setCurrentIndex(0)
        self.user_id.clear()
        self.user_name.clear()
        self.permission.setCurrentIndex(0)

