import pandas as pd

def analyze_excel(file_path):
    excel_data = pd.ExcelFile(file_path)
    sheet_names = excel_data.sheet_names
    
    analysis_results = {}
    for sheet_name in sheet_names:
        df = excel_data.parse(sheet_name)
        analysis_results[sheet_name] = {
            'columns': df.columns.tolist(),
            'head': df.head().to_dict(orient='records'),
            'info': df.info(verbose=True, show_counts=True, buf=None)
        }
    return analysis_results

if __name__ == '__main__':
    file_path = '/home/<USER>/upload/IOTSAnalysis.xlsx'
    results = analyze_excel(file_path)
    
    with open('/home/<USER>/excel_analysis_results.txt', 'w') as f:
        for sheet_name, data in results.items():
            f.write(f"Sheet: {sheet_name}\n")
            f.write(f"Columns: {data['columns']}\n")
            f.write(f"Head:\n{data['head']}\n")
            f.write(f"Info:\n{data['info']}\n")
            f.write("\n" + "-"*50 + "\n\n")


