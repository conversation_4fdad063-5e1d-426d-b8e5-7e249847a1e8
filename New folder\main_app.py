import sys
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QMain<PERSON>indow, QW<PERSON>t, QVBoxLayout, QLabel, QAction, QMenuBar, QStackedWidget
from PyQt5.QtCore import Qt
from data_entry_form import DataEntryForm
from data_display_table import DataDisplayTable

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("IOTS Desktop Application")
        self.setGeometry(100, 100, 1000, 700)

        # Set application direction to Right-to-Left
        QApplication.instance().setLayoutDirection(Qt.RightToLeft)

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout()
        self.central_widget.setLayout(self.layout)

        self.stacked_widget = QStackedWidget()
        self.layout.addWidget(self.stacked_widget)

        self.home_page = QLabel("مرحباً بك في تطبيق IOTS!\nWelcome to the IOTS Application!")
        self.home_page.setAlignment(Qt.AlignCenter)
        self.home_page.setStyleSheet("font-size: 24px; color: #333; padding: 50px;")
        self.stacked_widget.addWidget(self.home_page)

        self.data_entry_form = DataEntryForm()
        self.stacked_widget.addWidget(self.data_entry_form)

        self.data_display_table = DataDisplayTable()
        self.stacked_widget.addWidget(self.data_display_table)

        self._create_menu_bar()
        self._apply_styles()

    def _create_menu_bar(self):
        menu_bar = self.menuBar()
        menu_bar.setStyleSheet("QMenuBar { background-color: #f0f0f0; } QMenuBar::item { padding: 5px 10px; } QMenuBar::item:selected { background-color: #ddd; }")

        # File Menu
        file_menu = menu_bar.addMenu("&ملف - File")
        exit_action = QAction("&خروج - Exit", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Data Entry Menu
        data_entry_menu = menu_bar.addMenu("&إدخال البيانات - Data Entry")
        new_record_action = QAction("&إدخال سجل جديد - New Record", self)
        new_record_action.triggered.connect(lambda: self.stacked_widget.setCurrentWidget(self.data_entry_form))
        data_entry_menu.addAction(new_record_action)

        # Data View Menu
        data_view_menu = menu_bar.addMenu("&عرض البيانات - Data View")
        view_records_action = QAction("&عرض السجلات - View Records", self)
        view_records_action.triggered.connect(lambda: self.stacked_widget.setCurrentWidget(self.data_display_table))
        data_view_menu.addAction(view_records_action)

        # Home Menu
        home_menu = menu_bar.addMenu("&الرئيسية - Home")
        home_action = QAction("&الصفحة الرئيسية - Home Page", self)
        home_action.triggered.connect(lambda: self.stacked_widget.setCurrentWidget(self.home_page))
        home_menu.addAction(home_action)

        # Help Menu
        help_menu = menu_bar.addMenu("&مساعدة - Help")
        about_action = QAction("&حول - About", self)
        help_menu.addAction(about_action)

    def _apply_styles(self):
        self.setStyleSheet("""
            QMainWindow {
                background-color: #e0e0e0;
            }
            QMenuBar {
                background-color: #f0f0f0;
                border-bottom: 1px solid #ccc;
            }
            QMenuBar::item {
                padding: 8px 15px;
                background: transparent;
            }
            QMenuBar::item:selected {
                background: #c0c0c0;
            }
            QMenu {
                background-color: #f8f8f8;
                border: 1px solid #ccc;
                margin: 2px;
            }
            QMenu::item {
                padding: 8px 20px;
            }
            QMenu::item:selected {
                background-color: #e0e0e0;
            }
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                margin-top: 10px;
                border: 1px solid #ccc;
                border-radius: 5px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 3px;
                background-color: #e0e0e0;
            }
            QLineEdit, QComboBox, QDateEdit {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 3px;
            }
            QPushButton {
                padding: 8px 15px;
                border: none;
                border-radius: 5px;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                opacity: 0.8;
            }
            QTableWidget {
                border: 1px solid #ccc;
                border-radius: 5px;
                gridline-color: #eee;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 5px;
                border: 1px solid #ccc;
                font-weight: bold;
            }
        """)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())


