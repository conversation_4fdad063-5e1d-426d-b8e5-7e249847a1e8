CREATE TABLE `visa_type` (
    `id` INT PRIMARY KEY,
    `visa_type` VARCHAR(50)
);

CREATE TABLE `received_from` (
    `id` INT PRIMARY KEY,
    `received_from` VARCHAR(255)
);

CREATE TABLE `action_taken` (
    `id` INT PRIMARY KEY,
    `action_taken` VARCHAR(255)
);

CREATE TABLE `request_status` (
    `id` INT PRIMARY KEY,
    `request_status` VARCHAR(255)
);

CREATE TABLE `users` (
    `user_id` VARCHAR(50) PRIMARY KEY,
    `user_name` VARCHAR(50),
    `user_pass` VARCHAR(50),
    `permission` VARCHAR(10),
    `screen` VARCHAR(25)
);

CREATE TABLE `Main` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `head_incoming_no` VARCHAR(6),
    `letter` VARCHAR(3),
    `head_incoming_date` DATE,
    `visa_type_id` INT,
    FOREI<PERSON>N KEY (`visa_type_id`) REFERENCES `visa_type`(`id`)
);


