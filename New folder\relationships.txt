Inferred Relationships:

1.  **Main.visa_type** relates to **visa_type.id** (Many-to-One: A visa type in the Main sheet refers to an ID in the visa_type sheet).
2.  **Main.id** is likely the primary key for the Main sheet.
3.  **users.user_id** is likely the primary key for the users sheet.
4.  **visa_type.id** is the primary key for the visa_type sheet.
5.  **received_from.id** is the primary key for the received_from sheet.
6.  **action_taken.id** is the primary key for the action_taken sheet.
7.  **request_status.id** is the primary key for the request_status sheet.

Further investigation might be needed to confirm if other fields in 'Main' sheet like 'head_incoming_no', 'letter', 'head_incoming_date' are foreign keys to other tables not explicitly defined in the provided sheets, or if they are just descriptive fields.

