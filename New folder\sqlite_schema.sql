CREATE TABLE visa_type (
    id INTEGER PRIMARY KEY,
    visa_type TEXT
);

CREATE TABLE received_from (
    id INTEGER PRIMARY KEY,
    received_from TEXT
);

CREATE TABLE action_taken (
    id INTEGER PRIMARY KEY,
    action_taken TEXT
);

CREATE TABLE request_status (
    id INTEGER PRIMARY KEY,
    request_status TEXT
);

CREATE TABLE users (
    user_id TEXT PRIMARY KEY,
    user_name TEXT,
    user_pass TEXT,
    permission TEXT,
    screen TEXT
);

CREATE TABLE Main (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    head_incoming_no TEXT,
    letter TEXT,
    head_incoming_date TEXT,
    visa_type_id INTEGER,
    FOREIGN KEY (visa_type_id) REFERENCES visa_type(id)
);


