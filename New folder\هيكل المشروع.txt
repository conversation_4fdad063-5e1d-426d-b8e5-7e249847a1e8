InAndOutTrackingSystem/
│
├── main.py                   # نقطة البداية
├── config/
│   ├── database.py           # إعدادات الاتصال بـ SQLite و MySQL
│   └── sync.py               # منطق المزامنة
│
├── models/
│   ├── user.py
│   ├── request.py
│   └── enums.py              # مثل visa_type, action_taken, etc.
│
├── views/
│   ├── login.py
│   ├── dashboard.py
│   ├── request_form.py
│   └── reports.py
│
├── utils/
│   ├── helpers.py
│   └── rtl_support.py        # دعم الاتجاه من اليمين لليسار
│
├── db/
│   ├── local.db              # SQLite المحلي
│   └── schema.sql            # مخطط قاعدة البيانات
│
├── assets/
│   ├── icons/
│   └── styles/
│       └── stylesheet.qss    # تنسيقات الواجهة
│
└── requirements.txt          # مكتبات Python المطلوبة