# نظام متابعة الوارد والصادر (IOTS)
## In and Out Tracking System

نظام متكامل لمتابعة المراسلات والمعاملات (الوارد والصادر) يتكون من تطبيق ويب مركزي وتطبيق سطح مكتب يعمل بشكل متزامن.



## 🚀 التشغيل السريع

### الطريقة الأسهل (Windows)
1. انقر مرتين على `تشغيل_تطبيق_الويب.bat`
2. افتح المتصفح على http://localhost:5000
3. سجل الدخول: admin / admin123

### الطريقة التقليدية
```bash
python main.py
```

## 📁 ملفات مهمة

- `تشغيل_تطبيق_الويب.bat` - تشغيل تطبيق الويب
- `تشغيل_تطبيق_سطح_المكتب.bat` - تشغيل تطبيق سطح المكتب  
- `إعداد_قاعدة_البيانات.bat` - إعداد قاعدة البيانات
- `اختبار_النظام.bat` - اختبار النظام
- `تحسين_النظام.bat` - تحسين الأداء
- `دليل_المستخدم_السريع.md` - دليل الاستخدام
- `DEPLOYMENT_GUIDE.md` - دليل النشر المفصل

## 📊 حالة المشروع

✅ **مكتمل ومجرب**
- تطبيق الويب: جاهز للإنتاج
- تطبيق سطح المكتب: جاهز للاستخدام
- قاعدة البيانات: محسنة ومفهرسة
- نظام التقارير: يدعم Excel, PDF, CSV
- المزامنة: تعمل بين التطبيقين
- الاختبارات: شاملة ومتكاملة

آخر تحديث: 2025-08-04 20:35

## المميزات الرئيسية

### 🌐 تطبيق الويب
- **نظام مصادقة آمن** مع تشفير كلمات المرور
- **لوحة تحكم تفاعلية** مع إحصائيات حيوية
- **إدارة شاملة للمعاملات** (إنشاء، تعديل، حذف، بحث)
- **نظام صلاحيات متقدم** (مدير، مستخدم عادي)
- **بحث وفلترة متقدمة** للمعاملات
- **واجهة عربية متجاوبة** تدعم جميع الشاشات
- **تقارير قابلة للطباعة والتصدير**

### 🖥️ تطبيق سطح المكتب
- **نمط تشغيل مزدوج** (متصل/غير متصل)
- **مزامنة ذكية للبيانات** بين الخادم والجهاز المحلي
- **واجهة احترافية RTL** باستخدام PyQt6
- **إشعارات سطح المكتب** للمهام الجديدة
- **ملف تثبيت سهل الاستخدام**

## التقنيات المستخدمة

### Backend
- **Python 3.8+**
- **Flask** - إطار عمل الويب
- **MySQL** - قاعدة البيانات الرئيسية
- **SQLite** - قاعدة البيانات المحلية
- **Flask-Login** - إدارة جلسات المستخدمين
- **Werkzeug** - تشفير كلمات المرور

### Frontend
- **HTML5 & CSS3**
- **Bootstrap 5 RTL** - التصميم المتجاوب
- **JavaScript ES6+**
- **Chart.js** - الرسوم البيانية
- **Font Awesome** - الأيقونات
- **Cairo Font** - الخط العربي

### Desktop Application
- **PyQt6** - واجهة المستخدم الرسومية
- **PyInstaller** - تحويل إلى ملف تنفيذي
- **SQLite** - قاعدة البيانات المحلية

## متطلبات النظام

### الخادم
- **نظام التشغيل:** Windows/Linux/macOS
- **Python:** 3.8 أو أحدث
- **MySQL:** 5.7 أو أحدث
- **ذاكرة الوصول العشوائي:** 2GB كحد أدنى
- **مساحة القرص الصلب:** 1GB

### العميل (سطح المكتب)
- **نظام التشغيل:** Windows 10/11
- **ذاكرة الوصول العشوائي:** 1GB كحد أدنى
- **مساحة القرص الصلب:** 500MB

## التثبيت والإعداد

### 1. تحضير البيئة

```bash
# استنساخ المشروع
git clone https://github.com/your-repo/iots.git
cd iots

# إنشاء بيئة افتراضية
python -m venv venv

# تفعيل البيئة الافتراضية
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt
```

### 2. إعداد قاعدة البيانات

```bash
# تشغيل سكريبت إعداد قاعدة البيانات
python -c "from config.database import setup_database; setup_database()"
```

### 3. تشغيل التطبيق

```bash
# تشغيل التطبيق الرئيسي
python main.py

# أو تشغيل تطبيق الويب مباشرة
python web_app/app.py
```

## الاستخدام

### تسجيل الدخول الافتراضي

**مدير النظام:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

**مستخدم عادي:**
- اسم المستخدم: `user1`
- كلمة المرور: `user123`

### إنشاء معاملة جديدة

1. انتقل إلى "معاملة جديدة" من القائمة الرئيسية
2. املأ البيانات المطلوبة:
   - رقم وارد رئيس المصلحة
   - تاريخ الوارد
   - موضوع المعاملة
3. حدد التصنيفات والباحثين المسؤولين
4. احفظ المعاملة

### البحث والفلترة

- استخدم مربع البحث للبحث برقم الوارد أو الموضوع
- استخدم الفلاتر للبحث حسب التاريخ أو الحالة
- يمكن دمج عدة معايير بحث معاً

## هيكل المشروع

```
IOTS/
├── main.py                 # نقطة البداية الرئيسية
├── requirements.txt        # متطلبات Python
├── config/                 # ملفات الإعداد
│   ├── database.py        # إعدادات قاعدة البيانات
│   ├── sync.py           # نظام المزامنة
│   └── configuration.ini  # ملف الإعدادات
├── models/                # نماذج البيانات
│   ├── user.py           # نموذج المستخدم
│   ├── transaction.py    # نموذج المعاملة
│   └── enums.py          # الجداول المساعدة
├── web_app/              # تطبيق الويب
│   ├── app.py           # التطبيق الرئيسي
│   ├── templates/       # قوالب HTML
│   └── static/          # ملفات CSS/JS/Images
├── desktop_app/         # تطبيق سطح المكتب
├── db/                  # ملفات قاعدة البيانات
│   ├── schema.sql      # مخطط قاعدة البيانات
│   └── initial_data.sql # البيانات الأولية
└── docs/               # التوثيق
```

## قاعدة البيانات

### الجداول الرئيسية

- **users** - المستخدمين والصلاحيات
- **transactions** - المعاملات الرئيسية
- **visa_types** - أنواع التأشيرات
- **received_from_sources** - مصادر الورود
- **actions_taken** - الإجراءات المتخذة
- **request_statuses** - حالات الطلبات
- **transaction_logs** - سجل التغييرات
- **attachments** - المرفقات

### العلاقات

- كل معاملة مرتبطة بمستخدم منشئ وباحثين مسؤولين
- نظام مفاتيح خارجية محكم لضمان سلامة البيانات
- فهرسة شاملة لتحسين الأداء

## الأمان

- **تشفير كلمات المرور** باستخدام bcrypt
- **جلسات آمنة** مع انتهاء صلاحية تلقائي
- **التحقق من الصلاحيات** على مستوى كل عملية
- **حماية من SQL Injection** باستخدام Prepared Statements
- **التحقق من صحة البيانات** في الواجهة والخادم

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:

- **البريد الإلكتروني:** <EMAIL>
- **GitHub Issues:** [رابط المشاكل](https://github.com/your-repo/iots/issues)

## خارطة الطريق

### الإصدار القادم (v1.1)
- [ ] نظام التقارير المتقدم
- [ ] API RESTful كامل
- [ ] تطبيق الهاتف المحمول
- [ ] نظام الإشعارات المتقدم
- [ ] دعم اللغة الإنجليزية

### المستقبل
- [ ] الذكاء الاصطناعي لتصنيف المعاملات
- [ ] التكامل مع الأنظمة الخارجية
- [ ] نظام الموافقات الإلكترونية
- [ ] التوقيع الرقمي

---

**تم تطوير هذا النظام بواسطة فريق IOTS Development Team**

*آخر تحديث: يناير 2024*
