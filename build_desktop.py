#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت بناء تطبيق سطح المكتب
Desktop Application Build Script

يقوم ببناء ملف تنفيذي من تطبيق PyQt6
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_requirements():
    """التحقق من المتطلبات"""
    try:
        import PyInstaller
        print("✓ PyInstaller متوفر")
    except ImportError:
        print("✗ PyInstaller غير مثبت")
        print("يرجى تثبيته باستخدام: pip install pyinstaller")
        return False
    
    try:
        import PyQt6
        print("✓ PyQt6 متوفر")
    except ImportError:
        print("✗ PyQt6 غير مثبت")
        print("يرجى تثبيته باستخدام: pip install PyQt6")
        return False
    
    return True

def create_spec_file():
    """إنشاء ملف .spec لـ PyInstaller"""
    spec_content = """
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['desktop_app/main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('models', 'models'),
        ('db', 'db'),
        ('config/configuration.ini', '.'),
    ],
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui', 
        'PyQt6.QtWidgets',
        'mysql.connector',
        'sqlite3',
        'bcrypt',
        'werkzeug.security'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='IOTS_Desktop',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
"""
    
    with open('IOTS_Desktop.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ تم إنشاء ملف IOTS_Desktop.spec")

def build_executable():
    """بناء الملف التنفيذي"""
    try:
        print("بدء عملية البناء...")
        
        # تشغيل PyInstaller
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'IOTS_Desktop.spec'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ تم بناء التطبيق بنجاح")
            return True
        else:
            print("✗ فشل في بناء التطبيق")
            print("خطأ:", result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ خطأ في عملية البناء: {e}")
        return False

def create_installer():
    """إنشاء ملف التثبيت"""
    try:
        # إنشاء مجلد التوزيع
        dist_dir = Path("dist/IOTS_Setup")
        dist_dir.mkdir(parents=True, exist_ok=True)
        
        # نسخ الملف التنفيذي
        exe_path = Path("dist/IOTS_Desktop.exe")
        if exe_path.exists():
            shutil.copy2(exe_path, dist_dir / "IOTS_Desktop.exe")
        
        # نسخ ملف الإعدادات
        config_file = Path("config/configuration.ini")
        if config_file.exists():
            shutil.copy2(config_file, dist_dir / "configuration.ini")
        
        # إنشاء ملف README للمستخدم
        readme_content = """
# نظام متابعة الوارد والصادر (IOTS)
## دليل التثبيت والاستخدام

### متطلبات النظام:
- Windows 10 أو أحدث
- 2GB ذاكرة وصول عشوائي
- 500MB مساحة فارغة على القرص الصلب

### التثبيت:
1. انسخ جميع الملفات إلى مجلد على جهازك
2. تأكد من وجود ملف configuration.ini في نفس مجلد البرنامج
3. شغل IOTS_Desktop.exe

### بيانات تسجيل الدخول الافتراضية:
- المدير: admin / admin123
- مستخدم: user1 / user123

### الدعم الفني:
للحصول على المساعدة، يرجى التواصل مع فريق الدعم الفني.

تاريخ الإصدار: يناير 2024
الإصدار: 1.0.0
"""
        
        with open(dist_dir / "README.txt", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        # إنشاء ملف batch للتشغيل السريع
        batch_content = """@echo off
chcp 65001 > nul
echo تشغيل نظام متابعة الوارد والصادر...
IOTS_Desktop.exe
pause
"""
        
        with open(dist_dir / "تشغيل_النظام.bat", 'w', encoding='utf-8') as f:
            f.write(batch_content)
        
        print(f"✓ تم إنشاء حزمة التثبيت في: {dist_dir}")
        return True
        
    except Exception as e:
        print(f"✗ خطأ في إنشاء ملف التثبيت: {e}")
        return False

def cleanup():
    """تنظيف الملفات المؤقتة"""
    try:
        # حذف مجلدات البناء المؤقتة
        temp_dirs = ['build', '__pycache__']
        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                print(f"✓ تم حذف {temp_dir}")
        
        # حذف ملف .spec
        if os.path.exists('IOTS_Desktop.spec'):
            os.remove('IOTS_Desktop.spec')
            print("✓ تم حذف ملف .spec")
            
    except Exception as e:
        print(f"تحذير: لم يتم حذف بعض الملفات المؤقتة: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("بناء تطبيق سطح المكتب - نظام متابعة الوارد والصادر")
    print("=" * 60)
    
    # التحقق من المتطلبات
    if not check_requirements():
        return 1
    
    # إنشاء ملف .spec
    create_spec_file()
    
    # بناء التطبيق
    if not build_executable():
        return 1
    
    # إنشاء حزمة التثبيت
    if not create_installer():
        return 1
    
    # تنظيف الملفات المؤقتة
    cleanup()
    
    print("\n" + "=" * 60)
    print("✅ تم بناء التطبيق بنجاح!")
    print("📁 يمكنك العثور على الملفات في مجلد: dist/IOTS_Setup")
    print("🚀 لتشغيل التطبيق: dist/IOTS_Setup/IOTS_Desktop.exe")
    print("=" * 60)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
