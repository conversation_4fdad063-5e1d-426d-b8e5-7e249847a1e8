# -*- coding: utf-8 -*-
"""
إعدادات قاعدة البيانات
Database Configuration Module

يحتوي على إعدادات الاتصال بقواعد البيانات MySQL و SQLite
"""

import os
import sqlite3
import mysql.connector
from mysql.connector import Error
import configparser
from pathlib import Path

# إعدادات قاعدة البيانات الافتراضية
DEFAULT_CONFIG = {
    'mysql': {
        'host': 'localhost',
        'port': 3306,
        'database': 'iots_db',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    },
    'sqlite': {
        'database': 'db/local.db'
    }
}

class DatabaseConfig:
    """فئة إعدادات قاعدة البيانات"""
    
    def __init__(self):
        self.config_file = Path(__file__).parent / 'configuration.ini'
        self.load_config()
    
    def load_config(self):
        """تحميل الإعدادات من ملف configuration.ini"""
        self.config = configparser.ConfigParser()
        
        if self.config_file.exists():
            self.config.read(self.config_file, encoding='utf-8')
        else:
            self.create_default_config()
    
    def create_default_config(self):
        """إنشاء ملف إعدادات افتراضي"""
        self.config['MYSQL'] = DEFAULT_CONFIG['mysql']
        self.config['SQLITE'] = DEFAULT_CONFIG['sqlite']
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            self.config.write(f)
    
    def get_mysql_config(self):
        """الحصول على إعدادات MySQL"""
        return dict(self.config['MYSQL'])
    
    def get_sqlite_config(self):
        """الحصول على إعدادات SQLite"""
        return dict(self.config['SQLITE'])

class DatabaseConnection:
    """فئة الاتصال بقاعدة البيانات"""
    
    def __init__(self):
        self.db_config = DatabaseConfig()
        self.mysql_connection = None
        self.sqlite_connection = None
    
    def connect_mysql(self):
        """الاتصال بقاعدة بيانات MySQL"""
        try:
            config = self.db_config.get_mysql_config()
            self.mysql_connection = mysql.connector.connect(**config)
            
            if self.mysql_connection.is_connected():
                print("✓ تم الاتصال بقاعدة بيانات MySQL بنجاح")
                return self.mysql_connection
            
        except Error as e:
            print(f"✗ خطأ في الاتصال بـ MySQL: {e}")
            return None
    
    def connect_sqlite(self):
        """الاتصال بقاعدة بيانات SQLite"""
        try:
            config = self.db_config.get_sqlite_config()
            db_path = Path(__file__).parent.parent / config['database']
            
            # إنشاء مجلد db إذا لم يكن موجوداً
            db_path.parent.mkdir(exist_ok=True)
            
            self.sqlite_connection = sqlite3.connect(str(db_path))
            self.sqlite_connection.row_factory = sqlite3.Row  # للحصول على النتائج كـ dictionary
            
            print("✓ تم الاتصال بقاعدة بيانات SQLite بنجاح")
            return self.sqlite_connection
            
        except Exception as e:
            print(f"✗ خطأ في الاتصال بـ SQLite: {e}")
            return None
    
    def test_connection(self):
        """اختبار الاتصال بقواعد البيانات"""
        print("اختبار الاتصال بقواعد البيانات...")
        
        # اختبار MySQL
        mysql_conn = self.connect_mysql()
        mysql_status = "متصل" if mysql_conn else "غير متصل"
        
        # اختبار SQLite
        sqlite_conn = self.connect_sqlite()
        sqlite_status = "متصل" if sqlite_conn else "غير متصل"
        
        print(f"MySQL: {mysql_status}")
        print(f"SQLite: {sqlite_status}")
        
        # إغلاق الاتصالات
        if mysql_conn:
            mysql_conn.close()
        if sqlite_conn:
            sqlite_conn.close()
        
        return mysql_conn is not None, sqlite_conn is not None
    
    def close_connections(self):
        """إغلاق جميع الاتصالات"""
        if self.mysql_connection and self.mysql_connection.is_connected():
            self.mysql_connection.close()
            print("تم إغلاق اتصال MySQL")
        
        if self.sqlite_connection:
            self.sqlite_connection.close()
            print("تم إغلاق اتصال SQLite")

def setup_database():
    """إعداد قاعدة البيانات الأولي"""
    print("بدء إعداد قاعدة البيانات...")

    db_conn = DatabaseConnection()
    mysql_ok, sqlite_ok = db_conn.test_connection()

    if mysql_ok:
        print("إنشاء جداول MySQL...")
        success = create_mysql_tables()
        if success:
            print("✓ تم إنشاء جداول MySQL بنجاح")
            # إدراج البيانات الأولية
            insert_initial_data()
        else:
            print("✗ فشل في إنشاء جداول MySQL")

    if sqlite_ok:
        print("إنشاء جداول SQLite...")
        from config.sync import DataSynchronizer
        sync = DataSynchronizer()
        success, message = sync.create_sqlite_schema()
        if success:
            print("✓ تم إنشاء جداول SQLite بنجاح")
        else:
            print(f"✗ فشل في إنشاء جداول SQLite: {message}")

    db_conn.close_connections()

def create_mysql_tables():
    """إنشاء جداول MySQL"""
    try:
        db_conn = DatabaseConnection()
        conn = db_conn.connect_mysql()
        if not conn:
            return False

        cursor = conn.cursor()

        # قراءة وتنفيذ سكريبت إنشاء قاعدة البيانات
        schema_file = Path(__file__).parent.parent / 'db' / 'schema.sql'

        if schema_file.exists():
            with open(schema_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()

            # تقسيم الاستعلامات وتنفيذها
            statements = sql_content.split(';')
            for statement in statements:
                statement = statement.strip()
                if statement and not statement.startswith('--'):
                    try:
                        cursor.execute(statement)
                    except Exception as e:
                        if "already exists" not in str(e).lower():
                            print(f"تحذير في تنفيذ الاستعلام: {e}")

            conn.commit()
            cursor.close()
            conn.close()
            return True
        else:
            print("ملف schema.sql غير موجود")
            return False

    except Exception as e:
        print(f"خطأ في إنشاء جداول MySQL: {e}")
        return False

def insert_initial_data():
    """إدراج البيانات الأولية"""
    try:
        db_conn = DatabaseConnection()
        conn = db_conn.connect_mysql()
        if not conn:
            return False

        cursor = conn.cursor()

        # قراءة وتنفيذ سكريبت البيانات الأولية
        data_file = Path(__file__).parent.parent / 'db' / 'initial_data.sql'

        if data_file.exists():
            with open(data_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()

            # تقسيم الاستعلامات وتنفيذها
            statements = sql_content.split(';')
            for statement in statements:
                statement = statement.strip()
                if statement and not statement.startswith('--'):
                    try:
                        cursor.execute(statement)
                    except Exception as e:
                        if "duplicate entry" not in str(e).lower():
                            print(f"تحذير في إدراج البيانات: {e}")

            conn.commit()
            cursor.close()
            conn.close()
            print("✓ تم إدراج البيانات الأولية بنجاح")
            return True
        else:
            print("ملف initial_data.sql غير موجود")
            return False

    except Exception as e:
        print(f"خطأ في إدراج البيانات الأولية: {e}")
        return False

if __name__ == "__main__":
    setup_database()
