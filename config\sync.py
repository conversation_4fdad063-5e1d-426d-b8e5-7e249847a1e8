# -*- coding: utf-8 -*-
"""
نظام المزامنة
Synchronization System

يحتوي على منطق مزامنة البيانات بين MySQL و SQLite
"""

import json
import sqlite3
import mysql.connector
from datetime import datetime
from config.database import DatabaseConnection, DatabaseConfig

class DataSynchronizer:
    """فئة مزامنة البيانات"""
    
    def __init__(self):
        self.db_conn = DatabaseConnection()
        self.config = DatabaseConfig()
        self.sync_log = []
    
    def create_sqlite_schema(self):
        """إنشاء مخطط SQLite مطابق لـ MySQL"""
        try:
            sqlite_conn = self.db_conn.connect_sqlite()
            if not sqlite_conn:
                return False, "فشل الاتصال بـ SQLite"
            
            cursor = sqlite_conn.cursor()
            
            # إنشاء جدول المستخدمين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    user_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_name TEXT NOT NULL UNIQUE,
                    user_pass TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    email TEXT,
                    phone TEXT,
                    permission TEXT NOT NULL DEFAULT 'user',
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    last_login TEXT
                )
            """)
            
            # إنشاء جدول أنواع التأشيرات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS visa_types (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    visa_type TEXT NOT NULL UNIQUE,
                    description TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إنشاء جدول مصادر الورود
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS received_from_sources (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    received_from TEXT NOT NULL UNIQUE,
                    contact_info TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إنشاء جدول الإجراءات المتخذة
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS actions_taken (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    action_taken TEXT NOT NULL UNIQUE,
                    description TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إنشاء جدول حالات الطلبات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS request_statuses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    request_status TEXT NOT NULL UNIQUE,
                    status_color TEXT DEFAULT '#007bff',
                    description TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إنشاء جدول المعاملات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    head_incoming_no TEXT NOT NULL UNIQUE,
                    head_incoming_date TEXT NOT NULL,
                    subject TEXT NOT NULL,
                    researcher_notes TEXT,
                    priority TEXT DEFAULT 'medium',
                    due_date TEXT,
                    user_id INTEGER NOT NULL,
                    researcher_1_id INTEGER,
                    researcher_2_id INTEGER,
                    visa_type_id INTEGER,
                    received_from_id INTEGER,
                    action_taken_id INTEGER,
                    request_status_id INTEGER,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    completed_at TEXT,
                    FOREIGN KEY (user_id) REFERENCES users(user_id),
                    FOREIGN KEY (researcher_1_id) REFERENCES users(user_id),
                    FOREIGN KEY (researcher_2_id) REFERENCES users(user_id),
                    FOREIGN KEY (visa_type_id) REFERENCES visa_types(id),
                    FOREIGN KEY (received_from_id) REFERENCES received_from_sources(id),
                    FOREIGN KEY (action_taken_id) REFERENCES actions_taken(id),
                    FOREIGN KEY (request_status_id) REFERENCES request_statuses(id)
                )
            """)
            
            # إنشاء جدول سجل التغييرات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS transaction_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    transaction_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    action TEXT NOT NULL,
                    old_values TEXT,
                    new_values TEXT,
                    notes TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (transaction_id) REFERENCES transactions(id),
                    FOREIGN KEY (user_id) REFERENCES users(user_id)
                )
            """)
            
            # إنشاء جدول المرفقات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS attachments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    transaction_id INTEGER NOT NULL,
                    file_name TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    file_size INTEGER,
                    file_type TEXT,
                    uploaded_by INTEGER NOT NULL,
                    uploaded_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (transaction_id) REFERENCES transactions(id),
                    FOREIGN KEY (uploaded_by) REFERENCES users(user_id)
                )
            """)
            
            # إنشاء جدول المزامنة
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sync_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    table_name TEXT NOT NULL,
                    record_id INTEGER NOT NULL,
                    action TEXT NOT NULL,
                    sync_status TEXT DEFAULT 'pending',
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    synced_at TEXT
                )
            """)
            
            sqlite_conn.commit()
            cursor.close()
            sqlite_conn.close()
            
            return True, "تم إنشاء مخطط SQLite بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إنشاء مخطط SQLite: {str(e)}"
    
    def sync_from_mysql_to_sqlite(self):
        """مزامنة البيانات من MySQL إلى SQLite"""
        try:
            mysql_conn = self.db_conn.connect_mysql()
            sqlite_conn = self.db_conn.connect_sqlite()
            
            if not mysql_conn or not sqlite_conn:
                return False, "فشل الاتصال بقواعد البيانات"
            
            tables_to_sync = [
                'users', 'visa_types', 'received_from_sources', 
                'actions_taken', 'request_statuses', 'transactions'
            ]
            
            mysql_cursor = mysql_conn.cursor()
            sqlite_cursor = sqlite_conn.cursor()
            
            for table in tables_to_sync:
                # الحصول على البيانات من MySQL
                mysql_cursor.execute(f"SELECT * FROM {table}")
                mysql_data = mysql_cursor.fetchall()
                
                # الحصول على أسماء الأعمدة
                mysql_cursor.execute(f"DESCRIBE {table}")
                columns_info = mysql_cursor.fetchall()
                column_names = [col[0] for col in columns_info]
                
                # حذف البيانات الموجودة في SQLite
                sqlite_cursor.execute(f"DELETE FROM {table}")
                
                # إدراج البيانات الجديدة
                if mysql_data:
                    placeholders = ', '.join(['?' for _ in column_names])
                    query = f"INSERT INTO {table} ({', '.join(column_names)}) VALUES ({placeholders})"
                    
                    for row in mysql_data:
                        # تحويل التواريخ إلى نص
                        converted_row = []
                        for value in row:
                            if isinstance(value, datetime):
                                converted_row.append(value.isoformat())
                            else:
                                converted_row.append(value)
                        
                        sqlite_cursor.execute(query, converted_row)
                
                self.sync_log.append(f"تم مزامنة جدول {table}: {len(mysql_data)} سجل")
            
            sqlite_conn.commit()
            mysql_cursor.close()
            sqlite_cursor.close()
            mysql_conn.close()
            sqlite_conn.close()
            
            return True, "تم مزامنة البيانات من MySQL إلى SQLite بنجاح"
            
        except Exception as e:
            return False, f"خطأ في المزامنة: {str(e)}"
    
    def sync_from_sqlite_to_mysql(self):
        """مزامنة البيانات من SQLite إلى MySQL"""
        try:
            mysql_conn = self.db_conn.connect_mysql()
            sqlite_conn = self.db_conn.connect_sqlite()
            
            if not mysql_conn or not sqlite_conn:
                return False, "فشل الاتصال بقواعد البيانات"
            
            mysql_cursor = mysql_conn.cursor()
            sqlite_cursor = sqlite_conn.cursor()
            
            # البحث عن السجلات المعلقة للمزامنة
            sqlite_cursor.execute("""
                SELECT DISTINCT table_name FROM sync_log 
                WHERE sync_status = 'pending'
            """)
            
            tables_to_sync = [row[0] for row in sqlite_cursor.fetchall()]
            
            for table in tables_to_sync:
                # الحصول على السجلات المعلقة
                sqlite_cursor.execute("""
                    SELECT record_id, action FROM sync_log 
                    WHERE table_name = ? AND sync_status = 'pending'
                """, (table,))
                
                pending_records = sqlite_cursor.fetchall()
                
                for record_id, action in pending_records:
                    if action == 'INSERT' or action == 'UPDATE':
                        # الحصول على البيانات من SQLite
                        sqlite_cursor.execute(f"SELECT * FROM {table} WHERE id = ?", (record_id,))
                        record_data = sqlite_cursor.fetchone()
                        
                        if record_data:
                            # تحديث أو إدراج في MySQL
                            self._sync_record_to_mysql(mysql_cursor, table, record_data, action)
                    
                    elif action == 'DELETE':
                        # حذف من MySQL
                        mysql_cursor.execute(f"DELETE FROM {table} WHERE id = %s", (record_id,))
                    
                    # تحديث حالة المزامنة
                    sqlite_cursor.execute("""
                        UPDATE sync_log SET sync_status = 'completed', synced_at = datetime('now')
                        WHERE table_name = ? AND record_id = ? AND action = ?
                    """, (table, record_id, action))
            
            mysql_conn.commit()
            sqlite_conn.commit()
            
            mysql_cursor.close()
            sqlite_cursor.close()
            mysql_conn.close()
            sqlite_conn.close()
            
            return True, "تم مزامنة البيانات من SQLite إلى MySQL بنجاح"
            
        except Exception as e:
            return False, f"خطأ في المزامنة: {str(e)}"
    
    def _sync_record_to_mysql(self, mysql_cursor, table, record_data, action):
        """مزامنة سجل واحد إلى MySQL"""
        try:
            # الحصول على أسماء الأعمدة
            mysql_cursor.execute(f"DESCRIBE {table}")
            columns_info = mysql_cursor.fetchall()
            column_names = [col[0] for col in columns_info]
            
            if action == 'INSERT':
                placeholders = ', '.join(['%s' for _ in column_names])
                query = f"INSERT INTO {table} ({', '.join(column_names)}) VALUES ({placeholders})"
                mysql_cursor.execute(query, record_data)
            
            elif action == 'UPDATE':
                set_clause = ', '.join([f"{col} = %s" for col in column_names[1:]])  # تجاهل العمود الأول (ID)
                query = f"UPDATE {table} SET {set_clause} WHERE {column_names[0]} = %s"
                # ترتيب البيانات: البيانات الجديدة ثم ID
                update_data = list(record_data[1:]) + [record_data[0]]
                mysql_cursor.execute(query, update_data)
            
        except Exception as e:
            print(f"خطأ في مزامنة السجل: {e}")
    
    def log_change(self, table_name, record_id, action):
        """تسجيل تغيير للمزامنة اللاحقة"""
        try:
            sqlite_conn = self.db_conn.connect_sqlite()
            if not sqlite_conn:
                return
            
            cursor = sqlite_conn.cursor()
            cursor.execute("""
                INSERT INTO sync_log (table_name, record_id, action)
                VALUES (?, ?, ?)
            """, (table_name, record_id, action))
            
            sqlite_conn.commit()
            cursor.close()
            sqlite_conn.close()
            
        except Exception as e:
            print(f"خطأ في تسجيل التغيير: {e}")
    
    def get_sync_status(self):
        """الحصول على حالة المزامنة"""
        try:
            sqlite_conn = self.db_conn.connect_sqlite()
            if not sqlite_conn:
                return {}
            
            cursor = sqlite_conn.cursor()
            cursor.execute("""
                SELECT 
                    table_name,
                    COUNT(*) as total_changes,
                    COUNT(CASE WHEN sync_status = 'pending' THEN 1 END) as pending_changes,
                    COUNT(CASE WHEN sync_status = 'completed' THEN 1 END) as completed_changes
                FROM sync_log
                GROUP BY table_name
            """)
            
            results = cursor.fetchall()
            
            status = {}
            for row in results:
                status[row[0]] = {
                    'total_changes': row[1],
                    'pending_changes': row[2],
                    'completed_changes': row[3]
                }
            
            cursor.close()
            sqlite_conn.close()
            
            return status
            
        except Exception as e:
            print(f"خطأ في الحصول على حالة المزامنة: {e}")
            return {}
    
    def full_sync(self):
        """مزامنة كاملة في الاتجاهين"""
        results = []
        
        # إنشاء مخطط SQLite إذا لم يكن موجوداً
        success, message = self.create_sqlite_schema()
        results.append(f"إنشاء المخطط: {message}")
        
        # مزامنة من MySQL إلى SQLite
        success, message = self.sync_from_mysql_to_sqlite()
        results.append(f"MySQL → SQLite: {message}")
        
        # مزامنة من SQLite إلى MySQL
        success, message = self.sync_from_sqlite_to_mysql()
        results.append(f"SQLite → MySQL: {message}")
        
        return results

def test_sync():
    """اختبار نظام المزامنة"""
    print("اختبار نظام المزامنة...")
    
    sync = DataSynchronizer()
    results = sync.full_sync()
    
    for result in results:
        print(result)
    
    # عرض حالة المزامنة
    status = sync.get_sync_status()
    if status:
        print("\nحالة المزامنة:")
        for table, info in status.items():
            print(f"{table}: {info['pending_changes']} معلق من أصل {info['total_changes']}")

if __name__ == "__main__":
    test_sync()
