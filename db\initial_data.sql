-- البيانات الأولية لنظام متابعة الوارد والصادر
-- Initial Data for IOTS System

USE iots_db;

-- إدراج المستخدم الافتراضي (Admin)
INSERT INTO users (user_name, user_pass, full_name, email, permission) VALUES
('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSn9Uy/S', 'مدير النظام', '<EMAIL>', 'admin'),
('user1', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSn9Uy/S', 'باحث أول', '<EMAIL>', 'user'),
('user2', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSn9Uy/S', 'باحث ثاني', '<EMAIL>', 'user');

-- إدراج أنواع التأشيرات
INSERT INTO visa_types (visa_type, description) VALUES
('تأشيرة سياحية', 'تأشيرة للسياحة والزيارة'),
('تأشيرة عمل', 'تأشيرة للعمل والإقامة'),
('تأشيرة دراسة', 'تأشيرة للدراسة والتعليم'),
('تأشيرة عبور', 'تأشيرة للعبور والترانزيت'),
('تأشيرة علاج', 'تأشيرة للعلاج الطبي'),
('تأشيرة استثمار', 'تأشيرة للاستثمار والأعمال'),
('تأشيرة لم الشمل', 'تأشيرة لم الشمل العائلي'),
('تأشيرة دبلوماسية', 'تأشيرة للبعثات الدبلوماسية');

-- إدراج مصادر الورود
INSERT INTO received_from_sources (received_from, contact_info) VALUES
('وزارة الخارجية', 'هاتف: 123456789'),
('وزارة الداخلية', 'هاتف: 987654321'),
('السفارة الأمريكية', 'هاتف: 111222333'),
('السفارة البريطانية', 'هاتف: 444555666'),
('الق領صلية الفرنسية', 'هاتف: 777888999'),
('مكتب الهجرة', 'هاتف: 123123123'),
('الأمن العام', 'هاتف: 456456456'),
('مديرية الجوازات', 'هاتف: 789789789'),
('وزارة العدل', 'هاتف: 321321321'),
('المحكمة العليا', 'هاتف: 654654654');

-- إدراج الإجراءات المتخذة
INSERT INTO actions_taken (action_taken, description) VALUES
('تم الاستلام', 'تم استلام المعاملة وتسجيلها'),
('قيد الدراسة', 'المعاملة قيد الدراسة والمراجعة'),
('تم التوجيه للباحث', 'تم توجيه المعاملة للباحث المختص'),
('طلب معلومات إضافية', 'تم طلب معلومات أو مستندات إضافية'),
('تم الرد', 'تم الرد على المعاملة'),
('تم الإحالة', 'تم إحالة المعاملة لجهة أخرى'),
('تم الرفض', 'تم رفض الطلب مع بيان الأسباب'),
('تم القبول', 'تم قبول الطلب والموافقة عليه'),
('في انتظار المراجعة', 'في انتظار المراجعة النهائية'),
('تم الأرشفة', 'تم أرشفة المعاملة');

-- إدراج حالات الطلبات
INSERT INTO request_statuses (request_status, status_color, description) VALUES
('جديد', '#17a2b8', 'طلب جديد لم يتم البدء في معالجته'),
('قيد المعالجة', '#ffc107', 'الطلب قيد المعالجة والدراسة'),
('في انتظار الرد', '#fd7e14', 'في انتظار رد من جهة خارجية'),
('مكتمل', '#28a745', 'تم إكمال معالجة الطلب بنجاح'),
('مرفوض', '#dc3545', 'تم رفض الطلب'),
('معلق', '#6c757d', 'الطلب معلق لأسباب فنية أو إدارية'),
('ملغي', '#343a40', 'تم إلغاء الطلب'),
('محول', '#6f42c1', 'تم تحويل الطلب لجهة أخرى');

-- إدراج بعض المعاملات التجريبية
INSERT INTO transactions (
    head_incoming_no, 
    head_incoming_date, 
    subject, 
    researcher_notes, 
    user_id, 
    researcher_1_id, 
    visa_type_id, 
    received_from_id, 
    action_taken_id, 
    request_status_id,
    priority
) VALUES
('2024/001', '2024-01-15', 'طلب تأشيرة سياحية للسيد أحمد محمد', 'طلب مستوفي الشروط', 1, 2, 1, 1, 1, 1, 'medium'),
('2024/002', '2024-01-16', 'طلب تأشيرة عمل للسيدة فاطمة علي', 'يحتاج مراجعة الوثائق', 1, 3, 2, 2, 2, 2, 'high'),
('2024/003', '2024-01-17', 'طلب تأشيرة دراسة للطالب محمد حسن', 'طلب عاجل', 1, 2, 3, 3, 3, 2, 'urgent');

-- إنشاء فهارس إضافية لتحسين الأداء
CREATE INDEX idx_transactions_composite ON transactions(head_incoming_date, request_status_id, priority);
CREATE INDEX idx_transactions_researcher ON transactions(researcher_1_id, researcher_2_id);
CREATE INDEX idx_users_active ON users(is_active, permission);

-- إنشاء views مفيدة
CREATE VIEW v_transaction_details AS
SELECT 
    t.id,
    t.head_incoming_no,
    t.head_incoming_date,
    t.subject,
    t.researcher_notes,
    t.priority,
    t.due_date,
    u.full_name as created_by,
    r1.full_name as researcher_1,
    r2.full_name as researcher_2,
    vt.visa_type,
    rfs.received_from,
    at.action_taken,
    rs.request_status,
    rs.status_color,
    t.created_at,
    t.updated_at,
    t.completed_at
FROM transactions t
LEFT JOIN users u ON t.user_id = u.user_id
LEFT JOIN users r1 ON t.researcher_1_id = r1.user_id
LEFT JOIN users r2 ON t.researcher_2_id = r2.user_id
LEFT JOIN visa_types vt ON t.visa_type_id = vt.id
LEFT JOIN received_from_sources rfs ON t.received_from_id = rfs.id
LEFT JOIN actions_taken at ON t.action_taken_id = at.id
LEFT JOIN request_statuses rs ON t.request_status_id = rs.id;

-- إنشاء stored procedures مفيدة
DELIMITER //

CREATE PROCEDURE GetUserTransactions(IN user_id INT)
BEGIN
    SELECT * FROM v_transaction_details 
    WHERE researcher_1 = (SELECT full_name FROM users WHERE users.user_id = user_id)
       OR researcher_2 = (SELECT full_name FROM users WHERE users.user_id = user_id)
    ORDER BY created_at DESC;
END //

CREATE PROCEDURE GetTransactionStats()
BEGIN
    SELECT 
        COUNT(*) as total_transactions,
        COUNT(CASE WHEN request_status_id = 1 THEN 1 END) as new_transactions,
        COUNT(CASE WHEN request_status_id = 2 THEN 1 END) as in_progress,
        COUNT(CASE WHEN request_status_id = 4 THEN 1 END) as completed,
        COUNT(CASE WHEN due_date < CURDATE() AND request_status_id NOT IN (4,5,7) THEN 1 END) as overdue
    FROM transactions;
END //

DELIMITER ;
