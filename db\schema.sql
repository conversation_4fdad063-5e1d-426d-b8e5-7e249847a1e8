-- نظام متابعة الوارد والصادر (IOTS)
-- In and Out Tracking System Database Schema
-- إنشاء قاعدة البيانات والجداول

-- إن<PERSON><PERSON><PERSON> قاعدة البيانات
CREATE DATABASE IF NOT EXISTS iots_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE iots_db;

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    user_name VARCHAR(100) NOT NULL UNIQUE,
    user_pass VARCHAR(255) NOT NULL,
    full_name VARCHAR(200) NOT NULL,
    email VARCHAR(150),
    phone VARCHAR(20),
    permission ENUM('admin', 'user') NOT NULL DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    
    INDEX idx_user_name (user_name),
    INDEX idx_permission (permission),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول أنواع التأشيرات
CREATE TABLE IF NOT EXISTS visa_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    visa_type VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_visa_type (visa_type),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول مصادر الورود
CREATE TABLE IF NOT EXISTS received_from_sources (
    id INT AUTO_INCREMENT PRIMARY KEY,
    received_from VARCHAR(200) NOT NULL UNIQUE,
    contact_info TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_received_from (received_from),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الإجراءات المتخذة
CREATE TABLE IF NOT EXISTS actions_taken (
    id INT AUTO_INCREMENT PRIMARY KEY,
    action_taken VARCHAR(200) NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_action_taken (action_taken),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول حالات الطلبات
CREATE TABLE IF NOT EXISTS request_statuses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_status VARCHAR(100) NOT NULL UNIQUE,
    status_color VARCHAR(7) DEFAULT '#007bff',
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_request_status (request_status),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- الجدول الرئيسي للمعاملات
CREATE TABLE IF NOT EXISTS transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    head_incoming_no VARCHAR(50) NOT NULL UNIQUE,
    head_incoming_date DATE NOT NULL,
    subject TEXT NOT NULL,
    researcher_notes TEXT,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    due_date DATE,
    
    -- المفاتيح الخارجية
    user_id INT NOT NULL,
    researcher_1_id INT,
    researcher_2_id INT,
    visa_type_id INT,
    received_from_id INT,
    action_taken_id INT,
    request_status_id INT,
    
    -- تواريخ النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    
    -- القيود والفهارس
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE RESTRICT,
    FOREIGN KEY (researcher_1_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (researcher_2_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (visa_type_id) REFERENCES visa_types(id) ON DELETE SET NULL,
    FOREIGN KEY (received_from_id) REFERENCES received_from_sources(id) ON DELETE SET NULL,
    FOREIGN KEY (action_taken_id) REFERENCES actions_taken(id) ON DELETE SET NULL,
    FOREIGN KEY (request_status_id) REFERENCES request_statuses(id) ON DELETE SET NULL,
    
    INDEX idx_head_incoming_no (head_incoming_no),
    INDEX idx_head_incoming_date (head_incoming_date),
    INDEX idx_subject (subject(100)),
    INDEX idx_user_id (user_id),
    INDEX idx_researcher_1_id (researcher_1_id),
    INDEX idx_researcher_2_id (researcher_2_id),
    INDEX idx_visa_type_id (visa_type_id),
    INDEX idx_received_from_id (received_from_id),
    INDEX idx_action_taken_id (action_taken_id),
    INDEX idx_request_status_id (request_status_id),
    INDEX idx_priority (priority),
    INDEX idx_due_date (due_date),
    INDEX idx_created_at (created_at),
    INDEX idx_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول سجل التغييرات
CREATE TABLE IF NOT EXISTS transaction_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id INT NOT NULL,
    user_id INT NOT NULL,
    action VARCHAR(50) NOT NULL,
    old_values JSON,
    new_values JSON,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE RESTRICT,
    
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المرفقات
CREATE TABLE IF NOT EXISTS attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT,
    file_type VARCHAR(100),
    uploaded_by INT NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(user_id) ON DELETE RESTRICT,
    
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_uploaded_by (uploaded_by),
    INDEX idx_uploaded_at (uploaded_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
