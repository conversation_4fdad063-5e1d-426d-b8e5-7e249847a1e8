#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق سطح المكتب الرئيسي
Main Desktop Application

نقطة البداية لتطبيق سطح المكتب باستخدام PyQt6
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                                QHBoxLayout, QLabel, QPushButton, QTextEdit, 
                                QTableWidget, QTableWidgetItem, QHeaderView,
                                QMenuBar, QStatusBar, QMessageBox, QDialog,
                                QFormLayout, QLineEdit, QComboBox, QDateEdit,
                                QTab<PERSON>idget, QGroup<PERSON>ox, <PERSON><PERSON><PERSON><PERSON>ter, <PERSON><PERSON><PERSON><PERSON>)
    from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QDate
    from PyQt6.QtGui import QFont, QIcon, QPixmap, QAction
except ImportError:
    print("خطأ: PyQt6 غير مثبت. يرجى تثبيته باستخدام:")
    print("pip install PyQt6")
    sys.exit(1)

from config.database import DatabaseConnection
from config.sync import DataSynchronizer
from models.user import UserManager
from models.transaction import TransactionManager
from models.enums import EnumManager

class LoginDialog(QDialog):
    """نافذة تسجيل الدخول"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("تسجيل الدخول - نظام متابعة الوارد والصادر")
        self.setFixedSize(400, 300)
        self.setModal(True)
        
        # تطبيق الخط العربي
        font = QFont("Arial", 10)
        self.setFont(font)
        
        self.setup_ui()
        self.user_manager = UserManager(use_mysql=False)  # استخدام SQLite للبداية
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # شعار التطبيق
        title_label = QLabel("نظام متابعة الوارد والصادر")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin: 20px 0;
            }
        """)
        layout.addWidget(title_label)
        
        # نموذج تسجيل الدخول
        form_layout = QFormLayout()
        
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم")
        form_layout.addRow("اسم المستخدم:", self.username_edit)
        
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_edit.setPlaceholderText("أدخل كلمة المرور")
        form_layout.addRow("كلمة المرور:", self.password_edit)
        
        layout.addLayout(form_layout)
        
        # أزرار
        button_layout = QHBoxLayout()
        
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.clicked.connect(self.login)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.login_button)
        
        layout.addLayout(button_layout)
        
        # معلومات تجريبية
        info_label = QLabel("للاختبار: admin / admin123")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet("color: #7f8c8d; font-size: 12px; margin-top: 10px;")
        layout.addWidget(info_label)
        
        self.setLayout(layout)
        
        # ربط Enter بتسجيل الدخول
        self.username_edit.returnPressed.connect(self.login)
        self.password_edit.returnPressed.connect(self.login)
        
        # التركيز على حقل اسم المستخدم
        self.username_edit.setFocus()
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        
        if not username or not password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # محاولة تسجيل الدخول
        user, message = self.user_manager.authenticate_user(username, password)
        
        if user:
            self.user = user
            self.accept()
        else:
            QMessageBox.critical(self, "خطأ في تسجيل الدخول", message)

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self, user):
        super().__init__()
        self.user = user
        self.setWindowTitle(f"نظام متابعة الوارد والصادر - {user.full_name}")
        self.setGeometry(100, 100, 1200, 800)
        
        # تطبيق الخط العربي
        font = QFont("Arial", 9)
        self.setFont(font)
        
        # إعداد المدراء
        self.transaction_manager = TransactionManager(use_mysql=False)
        self.enum_manager = EnumManager(use_mysql=False)
        self.sync_manager = DataSynchronizer()
        
        self.setup_ui()
        self.setup_menu()
        self.setup_status_bar()
        self.load_data()
        
        # مؤقت للمزامنة التلقائية
        self.sync_timer = QTimer()
        self.sync_timer.timeout.connect(self.auto_sync)
        self.sync_timer.start(300000)  # كل 5 دقائق
    
    def setup_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # شريط الأدوات العلوي
        toolbar_layout = QHBoxLayout()
        
        self.new_transaction_btn = QPushButton("معاملة جديدة")
        self.new_transaction_btn.clicked.connect(self.new_transaction)
        self.new_transaction_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.clicked.connect(self.load_data)
        
        self.sync_btn = QPushButton("مزامنة")
        self.sync_btn.clicked.connect(self.manual_sync)
        
        toolbar_layout.addWidget(self.new_transaction_btn)
        toolbar_layout.addWidget(self.refresh_btn)
        toolbar_layout.addWidget(self.sync_btn)
        toolbar_layout.addStretch()
        
        main_layout.addLayout(toolbar_layout)
        
        # التبويبات الرئيسية
        self.tab_widget = QTabWidget()
        
        # تبويب المعاملات
        self.transactions_tab = self.create_transactions_tab()
        self.tab_widget.addTab(self.transactions_tab, "المعاملات")
        
        # تبويب الإحصائيات
        self.stats_tab = self.create_stats_tab()
        self.tab_widget.addTab(self.stats_tab, "الإحصائيات")
        
        main_layout.addWidget(self.tab_widget)
        
        central_widget.setLayout(main_layout)
    
    def create_transactions_tab(self):
        """إنشاء تبويب المعاملات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # جدول المعاملات
        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(7)
        self.transactions_table.setHorizontalHeaderLabels([
            "رقم الوارد", "التاريخ", "الموضوع", "الباحث", "الحالة", "الأولوية", "الإجراءات"
        ])
        
        # تخصيص عرض الأعمدة
        header = self.transactions_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
        
        layout.addWidget(self.transactions_table)
        widget.setLayout(layout)
        
        return widget
    
    def create_stats_tab(self):
        """إنشاء تبويب الإحصائيات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة الإحصائيات
        stats_group = QGroupBox("إحصائيات عامة")
        stats_layout = QHBoxLayout()
        
        self.total_label = QLabel("إجمالي المعاملات: 0")
        self.new_label = QLabel("جديدة: 0")
        self.progress_label = QLabel("قيد المعالجة: 0")
        self.completed_label = QLabel("مكتملة: 0")
        
        stats_layout.addWidget(self.total_label)
        stats_layout.addWidget(self.new_label)
        stats_layout.addWidget(self.progress_label)
        stats_layout.addWidget(self.completed_label)
        
        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)
        
        # مساحة للرسوم البيانية (يمكن تطويرها لاحقاً)
        chart_group = QGroupBox("الرسوم البيانية")
        chart_layout = QVBoxLayout()
        chart_placeholder = QLabel("سيتم إضافة الرسوم البيانية هنا")
        chart_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        chart_placeholder.setStyleSheet("color: #7f8c8d; font-style: italic;")
        chart_layout.addWidget(chart_placeholder)
        chart_group.setLayout(chart_layout)
        layout.addWidget(chart_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        
        return widget
    
    def setup_menu(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        
        new_action = QAction("معاملة جديدة", self)
        new_action.triggered.connect(self.new_transaction)
        file_menu.addAction(new_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("خروج", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة أدوات
        tools_menu = menubar.addMenu("أدوات")
        
        sync_action = QAction("مزامنة البيانات", self)
        sync_action.triggered.connect(self.manual_sync)
        tools_menu.addAction(sync_action)
        
        # قائمة مساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage(f"مرحباً {self.user.full_name}")
        
        # مؤشر حالة الاتصال
        self.connection_label = QLabel("غير متصل")
        self.connection_label.setStyleSheet("color: red;")
        self.status_bar.addPermanentWidget(self.connection_label)
    
    def load_data(self):
        """تحميل البيانات"""
        try:
            # تحميل المعاملات
            transactions = self.transaction_manager.get_all_transactions()
            self.populate_transactions_table(transactions)
            
            # تحديث الإحصائيات
            self.update_statistics(transactions)
            
            self.status_bar.showMessage("تم تحديث البيانات", 3000)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات: {str(e)}")
    
    def populate_transactions_table(self, transactions):
        """ملء جدول المعاملات"""
        self.transactions_table.setRowCount(len(transactions))
        
        for row, transaction in enumerate(transactions):
            self.transactions_table.setItem(row, 0, QTableWidgetItem(transaction.head_incoming_no or ""))
            self.transactions_table.setItem(row, 1, QTableWidgetItem(str(transaction.head_incoming_date) if transaction.head_incoming_date else ""))
            self.transactions_table.setItem(row, 2, QTableWidgetItem(transaction.subject or ""))
            self.transactions_table.setItem(row, 3, QTableWidgetItem("مسند" if transaction.researcher_1_id else "غير مسند"))
            self.transactions_table.setItem(row, 4, QTableWidgetItem(self.get_status_text(transaction.request_status_id)))
            self.transactions_table.setItem(row, 5, QTableWidgetItem(self.get_priority_text(transaction.priority)))
            
            # أزرار الإجراءات
            actions_btn = QPushButton("تعديل")
            actions_btn.clicked.connect(lambda checked, t_id=transaction.id: self.edit_transaction(t_id))
            self.transactions_table.setCellWidget(row, 6, actions_btn)
    
    def get_status_text(self, status_id):
        """الحصول على نص الحالة"""
        status_map = {1: "جديد", 2: "قيد المعالجة", 3: "في انتظار الرد", 4: "مكتمل"}
        return status_map.get(status_id, "غير محدد")
    
    def get_priority_text(self, priority):
        """الحصول على نص الأولوية"""
        priority_map = {"low": "منخفض", "medium": "متوسط", "high": "مرتفع", "urgent": "عاجل"}
        return priority_map.get(priority, "متوسط")
    
    def update_statistics(self, transactions):
        """تحديث الإحصائيات"""
        total = len(transactions)
        new_count = len([t for t in transactions if t.request_status_id == 1])
        progress_count = len([t for t in transactions if t.request_status_id == 2])
        completed_count = len([t for t in transactions if t.request_status_id == 4])
        
        self.total_label.setText(f"إجمالي المعاملات: {total}")
        self.new_label.setText(f"جديدة: {new_count}")
        self.progress_label.setText(f"قيد المعالجة: {progress_count}")
        self.completed_label.setText(f"مكتملة: {completed_count}")
    
    def new_transaction(self):
        """إنشاء معاملة جديدة"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة إنشاء معاملة جديدة قريباً")
    
    def edit_transaction(self, transaction_id):
        """تعديل معاملة"""
        QMessageBox.information(self, "قريباً", f"سيتم إضافة نافذة تعديل المعاملة رقم {transaction_id} قريباً")
    
    def manual_sync(self):
        """مزامنة يدوية"""
        try:
            self.sync_btn.setText("جاري المزامنة...")
            self.sync_btn.setEnabled(False)
            
            results = self.sync_manager.full_sync()
            
            message = "نتائج المزامنة:\n" + "\n".join(results)
            QMessageBox.information(self, "المزامنة", message)
            
            # إعادة تحميل البيانات
            self.load_data()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في المزامنة", str(e))
        finally:
            self.sync_btn.setText("مزامنة")
            self.sync_btn.setEnabled(True)
    
    def auto_sync(self):
        """مزامنة تلقائية"""
        try:
            self.sync_manager.sync_from_sqlite_to_mysql()
            self.connection_label.setText("متصل")
            self.connection_label.setStyleSheet("color: green;")
        except:
            self.connection_label.setText("غير متصل")
            self.connection_label.setStyleSheet("color: red;")
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        QMessageBox.about(self, "حول البرنامج", 
                         "نظام متابعة الوارد والصادر (IOTS)\n"
                         "الإصدار 1.0\n\n"
                         "نظام متكامل لإدارة المعاملات والمراسلات\n"
                         "تم تطويره باستخدام Python و PyQt6")

def run_desktop_app():
    """تشغيل تطبيق سطح المكتب"""
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي على التطبيق كاملاً
    font = QFont("Arial", 9)
    app.setFont(font)
    
    # تطبيق الستايل
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f8f9fa;
        }
        QTabWidget::pane {
            border: 1px solid #dee2e6;
            background-color: white;
        }
        QTabBar::tab {
            background-color: #e9ecef;
            padding: 8px 16px;
            margin-right: 2px;
        }
        QTabBar::tab:selected {
            background-color: white;
            border-bottom: 2px solid #007bff;
        }
        QTableWidget {
            gridline-color: #dee2e6;
            background-color: white;
        }
        QTableWidget::item {
            padding: 8px;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #dee2e6;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
    """)
    
    # عرض نافذة تسجيل الدخول
    login_dialog = LoginDialog()
    if login_dialog.exec() == QDialog.DialogCode.Accepted:
        # إنشاء النافذة الرئيسية
        main_window = MainWindow(login_dialog.user)
        main_window.show()
        
        return app.exec()
    else:
        return 0

if __name__ == "__main__":
    sys.exit(run_desktop_app())
