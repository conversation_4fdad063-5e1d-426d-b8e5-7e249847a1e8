#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تحميل الأصول المطلوبة للتطبيق
Download Required Assets Script

يقوم بتحميل Bootstrap, jQuery, Chart.js وغيرها من المكتبات المطلوبة
"""

import os
import requests
from pathlib import Path
import zipfile
import shutil

# مسارات الملفات
STATIC_DIR = Path("web_app/static")
CSS_DIR = STATIC_DIR / "css"
JS_DIR = STATIC_DIR / "js"
FONTS_DIR = STATIC_DIR / "fonts"
IMAGES_DIR = STATIC_DIR / "images"

# إنشاء المجلدات إذا لم تكن موجودة
for directory in [CSS_DIR, JS_DIR, FONTS_DIR, IMAGES_DIR]:
    directory.mkdir(parents=True, exist_ok=True)

# قائمة الملفات المطلوب تحميلها
ASSETS = {
    # Bootstrap 5 RTL
    "bootstrap.rtl.min.css": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css",
    "bootstrap.bundle.min.js": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js",
    
    # jQuery
    "jquery.min.js": "https://code.jquery.com/jquery-3.7.1.min.js",
    
    # Chart.js
    "chart.min.js": "https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js",
    
    # Font Awesome
    "fontawesome.min.css": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css",
}

def download_file(url, destination):
    """تحميل ملف من URL إلى المجلد المحدد"""
    try:
        print(f"تحميل {destination.name}...")
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(destination, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print(f"✓ تم تحميل {destination.name}")
        return True
    except Exception as e:
        print(f"✗ فشل تحميل {destination.name}: {e}")
        return False

def create_placeholder_files():
    """إنشاء ملفات بديلة في حالة فشل التحميل"""
    
    # Bootstrap RTL CSS بديل
    bootstrap_css = CSS_DIR / "bootstrap.rtl.min.css"
    if not bootstrap_css.exists():
        with open(bootstrap_css, 'w', encoding='utf-8') as f:
            f.write("""
/* Bootstrap RTL Placeholder */
.container-fluid { padding: 0 15px; }
.row { display: flex; flex-wrap: wrap; }
.col, .col-md-6, .col-lg-4 { flex: 1; padding: 0 15px; }
.btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; }
.btn-primary { background-color: #007bff; color: white; }
.card { border: 1px solid #dee2e6; border-radius: 8px; margin-bottom: 1rem; }
.card-header { padding: 1rem; background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; }
.card-body { padding: 1rem; }
.table { width: 100%; border-collapse: collapse; }
.table th, .table td { padding: 0.75rem; border-bottom: 1px solid #dee2e6; }
.navbar { background-color: #007bff; padding: 1rem 0; }
.navbar-brand, .nav-link { color: white; text-decoration: none; }
.form-control { width: 100%; padding: 0.75rem; border: 1px solid #ced4da; border-radius: 4px; }
.alert { padding: 1rem; margin-bottom: 1rem; border-radius: 4px; }
.alert-success { background-color: #d4edda; color: #155724; }
.alert-danger { background-color: #f8d7da; color: #721c24; }
.badge { padding: 0.25em 0.5em; border-radius: 0.25rem; font-size: 0.875em; }
.bg-primary { background-color: #007bff !important; }
.bg-success { background-color: #28a745 !important; }
.bg-danger { background-color: #dc3545 !important; }
.text-white { color: white !important; }
.text-center { text-align: center !important; }
.d-flex { display: flex !important; }
.justify-content-between { justify-content: space-between !important; }
.align-items-center { align-items: center !important; }
.mb-3 { margin-bottom: 1rem !important; }
.me-2 { margin-left: 0.5rem !important; }
""")
    
    # Bootstrap JS بديل
    bootstrap_js = JS_DIR / "bootstrap.bundle.min.js"
    if not bootstrap_js.exists():
        with open(bootstrap_js, 'w', encoding='utf-8') as f:
            f.write("""
// Bootstrap JS Placeholder
window.bootstrap = {
    Modal: function(element) {
        return {
            show: function() { element.style.display = 'block'; },
            hide: function() { element.style.display = 'none'; }
        };
    },
    Tooltip: function(element) { return {}; },
    Popover: function(element) { return {}; }
};
""")
    
    # jQuery بديل
    jquery_js = JS_DIR / "jquery.min.js"
    if not jquery_js.exists():
        with open(jquery_js, 'w', encoding='utf-8') as f:
            f.write("""
// jQuery Placeholder
window.$ = window.jQuery = function(selector) {
    if (typeof selector === 'function') {
        document.addEventListener('DOMContentLoaded', selector);
        return;
    }
    var elements = document.querySelectorAll(selector);
    return {
        length: elements.length,
        each: function(callback) {
            elements.forEach(callback);
            return this;
        },
        on: function(event, handler) {
            elements.forEach(el => el.addEventListener(event, handler));
            return this;
        },
        click: function(handler) {
            return this.on('click', handler);
        },
        val: function(value) {
            if (value !== undefined) {
                elements.forEach(el => el.value = value);
                return this;
            }
            return elements[0] ? elements[0].value : '';
        },
        text: function(text) {
            if (text !== undefined) {
                elements.forEach(el => el.textContent = text);
                return this;
            }
            return elements[0] ? elements[0].textContent : '';
        }
    };
};
""")
    
    # Chart.js بديل
    chart_js = JS_DIR / "chart.min.js"
    if not chart_js.exists():
        with open(chart_js, 'w', encoding='utf-8') as f:
            f.write("""
// Chart.js Placeholder
window.Chart = function(ctx, config) {
    console.log('Chart.js placeholder - chart would be rendered here');
    return {
        update: function() {},
        destroy: function() {}
    };
};
""")
    
    # Font Awesome CSS بديل
    fontawesome_css = CSS_DIR / "fontawesome.min.css"
    if not fontawesome_css.exists():
        with open(fontawesome_css, 'w', encoding='utf-8') as f:
            f.write("""
/* Font Awesome Placeholder */
.fas, .far, .fab {
    font-family: 'Font Awesome 6 Free', sans-serif;
    font-weight: 900;
    display: inline-block;
}
.fas:before { content: "\\f007"; } /* default user icon */
.fa-user:before { content: "\\f007"; }
.fa-home:before { content: "\\f015"; }
.fa-list:before { content: "\\f03a"; }
.fa-plus:before { content: "\\f067"; }
.fa-edit:before { content: "\\f044"; }
.fa-trash:before { content: "\\f1f8"; }
.fa-search:before { content: "\\f002"; }
.fa-save:before { content: "\\f0c7"; }
.fa-times:before { content: "\\f00d"; }
.fa-check:before { content: "\\f00c"; }
.fa-eye:before { content: "\\f06e"; }
.fa-cog:before { content: "\\f013"; }
.fa-sign-out-alt:before { content: "\\f2f5"; }
""")

def main():
    """الدالة الرئيسية لتحميل الأصول"""
    print("بدء تحميل الأصول المطلوبة...")
    print("=" * 50)
    
    success_count = 0
    total_count = len(ASSETS)
    
    for filename, url in ASSETS.items():
        if filename.endswith('.css'):
            destination = CSS_DIR / filename
        elif filename.endswith('.js'):
            destination = JS_DIR / filename
        else:
            destination = STATIC_DIR / filename
        
        if download_file(url, destination):
            success_count += 1
    
    print("=" * 50)
    print(f"تم تحميل {success_count} من أصل {total_count} ملف بنجاح")
    
    if success_count < total_count:
        print("إنشاء ملفات بديلة للملفات التي فشل تحميلها...")
        create_placeholder_files()
        print("✓ تم إنشاء الملفات البديلة")
    
    # إنشاء ملف فارغ للخطوط
    fonts_readme = FONTS_DIR / "README.md"
    if not fonts_readme.exists():
        with open(fonts_readme, 'w', encoding='utf-8') as f:
            f.write("# مجلد الخطوط\n\nضع ملفات الخطوط العربية هنا (Cairo font)")
    
    # إنشاء ملف فارغ للصور
    images_readme = IMAGES_DIR / "README.md"
    if not images_readme.exists():
        with open(images_readme, 'w', encoding='utf-8') as f:
            f.write("# مجلد الصور\n\nضع صور التطبيق والأيقونات هنا")
    
    print("\n✅ تم الانتهاء من إعداد جميع الأصول المطلوبة!")
    print("\nيمكنك الآن تشغيل التطبيق باستخدام:")
    print("python main.py")

if __name__ == "__main__":
    main()
