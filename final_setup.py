#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الإعداد النهائي للنظام
Final System Setup

يقوم بالإعداد النهائي وإنشاء ملفات التشغيل والتوثيق
"""

import os
import sys
import shutil
from pathlib import Path
from datetime import datetime

def create_final_structure():
    """إنشاء الهيكل النهائي للمشروع"""
    print("إنشاء الهيكل النهائي للمشروع...")
    
    # إنشاء المجلدات المطلوبة
    directories = [
        "logs",
        "backups", 
        "uploads",
        "exports",
        "temp"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        
        # إنشاء ملف README في كل مجلد
        readme_content = {
            "logs": "# مجلد السجلات\nيحتوي على ملفات سجل النظام والأخطاء",
            "backups": "# مجلد النسخ الاحتياطية\nيحتوي على النسخ الاحتياطية لقاعدة البيانات",
            "uploads": "# مجلد الرفع\nيحتوي على الملفات المرفوعة من المستخدمين",
            "exports": "# مجلد التصدير\nيحتوي على الملفات المصدرة والتقارير",
            "temp": "# مجلد مؤقت\nيحتوي على الملفات المؤقتة (يتم حذفها دورياً)"
        }
        
        readme_file = Path(directory) / "README.md"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content.get(directory, f"# مجلد {directory}"))
    
    print("✓ تم إنشاء الهيكل النهائي")

def create_startup_scripts():
    """إنشاء سكريبتات التشغيل"""
    print("إنشاء سكريبتات التشغيل...")
    
    # سكريبت تشغيل تطبيق الويب (Windows)
    web_bat = """@echo off
chcp 65001 > nul
title نظام متابعة الوارد والصادر - تطبيق الويب
echo.
echo ========================================
echo   نظام متابعة الوارد والصادر (IOTS)
echo   تطبيق الويب
echo ========================================
echo.
echo جاري تشغيل التطبيق...
echo يمكنك الوصول للتطبيق على: http://localhost:5000
echo.
echo للإيقاف اضغط Ctrl+C
echo.

python main.py

echo.
echo تم إيقاف التطبيق
pause
"""
    
    with open("تشغيل_تطبيق_الويب.bat", "w", encoding="utf-8") as f:
        f.write(web_bat)
    
    # سكريبت تشغيل تطبيق سطح المكتب (Windows)
    desktop_bat = """@echo off
chcp 65001 > nul
title نظام متابعة الوارد والصادر - تطبيق سطح المكتب
echo.
echo ========================================
echo   نظام متابعة الوارد والصادر (IOTS)
echo   تطبيق سطح المكتب
echo ========================================
echo.
echo جاري تشغيل التطبيق...

python -c "from desktop_app.main import run_desktop_app; run_desktop_app()"

echo.
echo تم إغلاق التطبيق
pause
"""
    
    with open("تشغيل_تطبيق_سطح_المكتب.bat", "w", encoding="utf-8") as f:
        f.write(desktop_bat)
    
    # سكريبت إعداد قاعدة البيانات (Windows)
    db_setup_bat = """@echo off
chcp 65001 > nul
title إعداد قاعدة البيانات - IOTS
echo.
echo ========================================
echo   إعداد قاعدة البيانات
echo   نظام متابعة الوارد والصادر
echo ========================================
echo.
echo جاري إعداد قاعدة البيانات...

python -c "from config.database import setup_database; setup_database()"

echo.
echo انتهى إعداد قاعدة البيانات
pause
"""
    
    with open("إعداد_قاعدة_البيانات.bat", "w", encoding="utf-8") as f:
        f.write(db_setup_bat)
    
    # سكريبت الاختبار (Windows)
    test_bat = """@echo off
chcp 65001 > nul
title اختبار النظام - IOTS
echo.
echo ========================================
echo   اختبار النظام
echo   نظام متابعة الوارد والصادر
echo ========================================
echo.
echo جاري تشغيل الاختبارات...

python test_system.py

echo.
echo انتهت الاختبارات
pause
"""
    
    with open("اختبار_النظام.bat", "w", encoding="utf-8") as f:
        f.write(test_bat)
    
    # سكريبت التحسين (Windows)
    optimize_bat = """@echo off
chcp 65001 > nul
title تحسين النظام - IOTS
echo.
echo ========================================
echo   تحسين النظام
echo   نظام متابعة الوارد والصادر
echo ========================================
echo.

python optimize_system.py

echo.
echo انتهى التحسين
pause
"""
    
    with open("تحسين_النظام.bat", "w", encoding="utf-8") as f:
        f.write(optimize_bat)
    
    # سكريبت Linux/macOS
    web_sh = """#!/bin/bash
echo "========================================"
echo "  نظام متابعة الوارد والصادر (IOTS)"
echo "  تطبيق الويب"
echo "========================================"
echo
echo "جاري تشغيل التطبيق..."
echo "يمكنك الوصول للتطبيق على: http://localhost:5000"
echo
echo "للإيقاف اضغط Ctrl+C"
echo

python3 main.py
"""
    
    with open("run_web_app.sh", "w", encoding="utf-8") as f:
        f.write(web_sh)
    
    # جعل الملف قابل للتنفيذ
    os.chmod("run_web_app.sh", 0o755)
    
    print("✓ تم إنشاء سكريبتات التشغيل")

def create_configuration_templates():
    """إنشاء قوالب الإعدادات"""
    print("إنشاء قوالب الإعدادات...")
    
    # قالب إعدادات الإنتاج
    production_config = """[MYSQL]
host = localhost
port = 3306
database = iots_production
user = iots_user
password = CHANGE_THIS_PASSWORD
charset = utf8mb4
collation = utf8mb4_unicode_ci

[SQLITE]
database = db/production.db

[APPLICATION]
name = نظام متابعة الوارد والصادر
version = 1.0.0
debug = false
secret_key = CHANGE_THIS_SECRET_KEY_IN_PRODUCTION

[SECURITY]
password_hash_method = bcrypt
session_timeout = 3600
max_login_attempts = 5
require_https = true

[SYNC]
auto_sync_interval = 300
conflict_resolution = latest_wins
backup_before_sync = true

[LOGGING]
level = INFO
file = logs/production.log
max_size = 10MB
backup_count = 5
"""
    
    with open("config/production.ini", "w", encoding="utf-8") as f:
        f.write(production_config)
    
    # قالب إعدادات التطوير
    development_config = """[MYSQL]
host = localhost
port = 3306
database = iots_development
user = root
password = 
charset = utf8mb4
collation = utf8mb4_unicode_ci

[SQLITE]
database = db/development.db

[APPLICATION]
name = نظام متابعة الوارد والصادر - تطوير
version = 1.0.0-dev
debug = true
secret_key = development-secret-key

[SECURITY]
password_hash_method = bcrypt
session_timeout = 7200
max_login_attempts = 10
require_https = false

[SYNC]
auto_sync_interval = 60
conflict_resolution = latest_wins
backup_before_sync = false

[LOGGING]
level = DEBUG
file = logs/development.log
max_size = 5MB
backup_count = 3
"""
    
    with open("config/development.ini", "w", encoding="utf-8") as f:
        f.write(development_config)
    
    print("✓ تم إنشاء قوالب الإعدادات")

def create_documentation():
    """إنشاء ملفات التوثيق الإضافية"""
    print("إنشاء ملفات التوثيق...")
    
    # دليل المستخدم السريع
    user_guide = f"""# دليل المستخدم السريع
## نظام متابعة الوارد والصادر (IOTS)

### بيانات تسجيل الدخول الافتراضية

**مدير النظام:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

**مستخدم عادي:**
- اسم المستخدم: `user1`
- كلمة المرور: `user123`

### التشغيل السريع

#### تطبيق الويب
1. انقر مرتين على `تشغيل_تطبيق_الويب.bat`
2. افتح المتصفح على http://localhost:5000
3. سجل الدخول باستخدام البيانات أعلاه

#### تطبيق سطح المكتب
1. انقر مرتين على `تشغيل_تطبيق_سطح_المكتب.bat`
2. سجل الدخول باستخدام البيانات أعلاه

### الوظائف الأساسية

#### إنشاء معاملة جديدة
1. اذهب إلى "معاملة جديدة"
2. املأ البيانات المطلوبة:
   - رقم وارد رئيس المصلحة
   - تاريخ الوارد
   - موضوع المعاملة
3. اختر التصنيفات والباحثين
4. احفظ المعاملة

#### البحث في المعاملات
1. اذهب إلى "المعاملات"
2. استخدم مربع البحث أو الفلاتر
3. اعرض النتائج أو صدرها

#### إنشاء التقارير
1. اذهب إلى "التقارير"
2. اختر نوع التقرير
3. حدد الفلاتر والتنسيق
4. احفظ أو اطبع التقرير

### الدعم الفني
للحصول على المساعدة، راجع:
- ملف README.md للتوثيق الكامل
- ملف DEPLOYMENT_GUIDE.md لدليل النشر
- مجلد docs للوثائق التفصيلية

تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d')}
الإصدار: 1.0.0
"""
    
    with open("دليل_المستخدم_السريع.md", "w", encoding="utf-8") as f:
        f.write(user_guide)
    
    # ملف الأسئلة الشائعة
    faq = """# الأسئلة الشائعة (FAQ)
## نظام متابعة الوارد والصادر

### أسئلة عامة

**س: ما هو نظام متابعة الوارد والصادر؟**
ج: هو نظام متكامل لإدارة ومتابعة المراسلات والمعاملات الواردة والصادرة في المؤسسات.

**س: هل يمكن استخدام النظام بدون اتصال بالإنترنت؟**
ج: نعم، يمكن استخدام تطبيق سطح المكتب بدون اتصال، مع إمكانية المزامنة لاحقاً.

### أسئلة تقنية

**س: ما هي متطلبات النظام؟**
ج: Python 3.8+، MySQL (اختياري)، 4GB RAM، 2GB مساحة قرص.

**س: كيف أغير كلمة مرور المدير؟**
ج: استخدم واجهة الويب أو قم بتشغيل سكريبت تحديث كلمة المرور.

**س: كيف أنشئ نسخة احتياطية؟**
ج: استخدم أدوات MySQL أو انسخ ملف SQLite من مجلد db.

### مشاكل شائعة

**س: لا يمكنني الاتصال بقاعدة البيانات**
ج: تأكد من تشغيل خادم MySQL وصحة بيانات الاتصال في configuration.ini.

**س: التطبيق بطيء**
ج: شغل أداة التحسين optimize_system.py أو تحقق من موارد النظام.

**س: لا تظهر النصوص العربية بشكل صحيح**
ج: تأكد من تثبيت خط Cairo أو استخدم خط بديل مثل Arial.
"""
    
    with open("الأسئلة_الشائعة.md", "w", encoding="utf-8") as f:
        f.write(faq)
    
    print("✓ تم إنشاء ملفات التوثيق")

def create_version_info():
    """إنشاء معلومات الإصدار"""
    print("إنشاء معلومات الإصدار...")
    
    version_info = {
        "name": "نظام متابعة الوارد والصادر",
        "name_en": "In and Out Tracking System",
        "acronym": "IOTS",
        "version": "1.0.0",
        "build_date": datetime.now().isoformat(),
        "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        "components": {
            "web_app": "1.0.0",
            "desktop_app": "1.0.0", 
            "database": "1.0.0",
            "reports": "1.0.0"
        },
        "features": [
            "إدارة المعاملات",
            "نظام المستخدمين والصلاحيات",
            "البحث والفلترة المتقدمة",
            "التقارير والإحصائيات",
            "المزامنة بين التطبيقات",
            "واجهة عربية متجاوبة",
            "تطبيق سطح مكتب"
        ],
        "supported_databases": ["MySQL", "SQLite"],
        "supported_platforms": ["Windows", "Linux", "macOS"],
        "license": "MIT",
        "author": "IOTS Development Team",
        "contact": "<EMAIL>"
    }
    
    import json
    with open("version_info.json", "w", encoding="utf-8") as f:
        json.dump(version_info, f, ensure_ascii=False, indent=2)
    
    print("✓ تم إنشاء معلومات الإصدار")

def create_final_readme():
    """إنشاء ملف README نهائي محدث"""
    print("تحديث ملف README...")
    
    # قراءة README الحالي
    current_readme = ""
    if Path("README.md").exists():
        with open("README.md", "r", encoding="utf-8") as f:
            current_readme = f.read()
    
    # إضافة معلومات التشغيل السريع
    quick_start = f"""

## 🚀 التشغيل السريع

### الطريقة الأسهل (Windows)
1. انقر مرتين على `تشغيل_تطبيق_الويب.bat`
2. افتح المتصفح على http://localhost:5000
3. سجل الدخول: admin / admin123

### الطريقة التقليدية
```bash
python main.py
```

## 📁 ملفات مهمة

- `تشغيل_تطبيق_الويب.bat` - تشغيل تطبيق الويب
- `تشغيل_تطبيق_سطح_المكتب.bat` - تشغيل تطبيق سطح المكتب  
- `إعداد_قاعدة_البيانات.bat` - إعداد قاعدة البيانات
- `اختبار_النظام.bat` - اختبار النظام
- `تحسين_النظام.bat` - تحسين الأداء
- `دليل_المستخدم_السريع.md` - دليل الاستخدام
- `DEPLOYMENT_GUIDE.md` - دليل النشر المفصل

## 📊 حالة المشروع

✅ **مكتمل ومجرب**
- تطبيق الويب: جاهز للإنتاج
- تطبيق سطح المكتب: جاهز للاستخدام
- قاعدة البيانات: محسنة ومفهرسة
- نظام التقارير: يدعم Excel, PDF, CSV
- المزامنة: تعمل بين التطبيقين
- الاختبارات: شاملة ومتكاملة

آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M')}
"""
    
    # دمج المحتوى
    if "## 🚀 التشغيل السريع" not in current_readme:
        # إضافة القسم الجديد بعد المقدمة
        lines = current_readme.split('\n')
        insert_index = -1
        
        for i, line in enumerate(lines):
            if line.startswith('## المميزات الرئيسية') or line.startswith('## Features'):
                insert_index = i
                break
        
        if insert_index > 0:
            lines.insert(insert_index, quick_start)
            updated_readme = '\n'.join(lines)
        else:
            updated_readme = current_readme + quick_start
        
        with open("README.md", "w", encoding="utf-8") as f:
            f.write(updated_readme)
    
    print("✓ تم تحديث ملف README")

def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("الإعداد النهائي لنظام متابعة الوارد والصادر (IOTS)")
    print("="*60)
    
    try:
        create_final_structure()
        create_startup_scripts()
        create_configuration_templates()
        create_documentation()
        create_version_info()
        create_final_readme()
        
        print("\n" + "="*60)
        print("🎉 تم الإعداد النهائي بنجاح!")
        print("="*60)
        
        print("\n📋 ملخص ما تم إنشاؤه:")
        print("✓ هيكل المجلدات النهائي")
        print("✓ سكريبتات التشغيل (.bat و .sh)")
        print("✓ قوالب الإعدادات (إنتاج وتطوير)")
        print("✓ ملفات التوثيق والمساعدة")
        print("✓ معلومات الإصدار")
        print("✓ ملف README محدث")
        
        print("\n🚀 للبدء:")
        print("1. Windows: انقر مرتين على تشغيل_تطبيق_الويب.bat")
        print("2. Linux/macOS: ./run_web_app.sh")
        print("3. أو: python main.py")
        
        print("\n📖 للمساعدة:")
        print("- دليل_المستخدم_السريع.md")
        print("- DEPLOYMENT_GUIDE.md")
        print("- الأسئلة_الشائعة.md")
        
        print("\n🎯 النظام جاهز للاستخدام!")
        
    except Exception as e:
        print(f"\n❌ خطأ في الإعداد النهائي: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
