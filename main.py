#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام متابعة الوارد والصادر (IOTS)
In and Out Tracking System

نقطة البداية الرئيسية للنظام
"""

import sys
import os
from pathlib import Path

# إعداد الترميز للـ console في Windows
if sys.platform.startswith('win'):
    try:
        # تعيين ترميز UTF-8 للـ console
        os.system('chcp 65001 > nul')
        # إعداد متغيرات البيئة
        os.environ['PYTHONIOENCODING'] = 'utf-8'
    except:
        pass

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def safe_print(text_ar, text_en=None):
    """طباعة آمنة للنصوص العربية مع fallback للإنجليزية"""
    try:
        print(text_ar)
    except UnicodeEncodeError:
        if text_en:
            print(text_en)
        else:
            # تحويل النص العربي إلى ASCII إذا لم يكن هناك نص إنجليزي
            print(text_ar.encode('ascii', 'ignore').decode('ascii'))

def main():
    """نقطة البداية الرئيسية"""
    safe_print("="*60)
    safe_print("مرحباً بك في نظام متابعة الوارد والصادر (IOTS)", "Welcome to In and Out Tracking System (IOTS)")
    safe_print("Welcome to In and Out Tracking System (IOTS)")
    safe_print("="*60)
    print()

    # التحقق من وجود المتطلبات
    try:
        import flask
        import PyQt6
        import mysql.connector
        import sqlite3
        safe_print("✓ جميع المتطلبات متوفرة", "✓ All requirements available")
    except ImportError as e:
        safe_print(f"✗ خطأ في المتطلبات: {e}", f"✗ Requirements error: {e}")
        safe_print("يرجى تثبيت المتطلبات باستخدام: pip install -r requirements.txt",
                  "Please install requirements using: pip install -r requirements.txt")
        return

    # عرض خيارات التشغيل
    print()
    safe_print("خيارات التشغيل:", "Runtime Options:")
    safe_print("1. تشغيل تطبيق الويب (Web Application)", "1. Run Web Application")
    safe_print("2. تشغيل تطبيق سطح المكتب (Desktop Application)", "2. Run Desktop Application")
    safe_print("3. إعداد قاعدة البيانات (Database Setup)", "3. Database Setup")
    safe_print("4. خروج (Exit)", "4. Exit")

    try:
        choice = input("\nاختر رقم الخيار (Choose option): ")
    except UnicodeEncodeError:
        choice = input("\nChoose option (1-4): ")
    
    if choice == "1":
        print()
        safe_print("جاري تشغيل تطبيق الويب...", "Starting web application...")
        safe_print("يمكنك الوصول للتطبيق على: http://localhost:5000", "Access the application at: http://localhost:5000")
        safe_print("للإيقاف اضغط Ctrl+C", "Press Ctrl+C to stop")
        print()

        from web_app.app import create_app
        app = create_app()
        app.run(host='0.0.0.0', port=5000, debug=True)

    elif choice == "2":
        print()
        safe_print("جاري تشغيل تطبيق سطح المكتب...", "Starting desktop application...")

        from desktop_app.main import run_desktop_app
        run_desktop_app()

    elif choice == "3":
        print()
        safe_print("جاري إعداد قاعدة البيانات...", "Setting up database...")

        from config.database import setup_database
        setup_database()

    elif choice == "4":
        print()
        safe_print("شكراً لاستخدام النظام!", "Thank you for using the system!")
        sys.exit(0)

    else:
        print()
        safe_print("خيار غير صحيح!", "Invalid choice!")
        main()

if __name__ == "__main__":
    main()
