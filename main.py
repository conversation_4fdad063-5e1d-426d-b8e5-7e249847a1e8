#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام متابعة الوارد والصادر (IOTS)
In and Out Tracking System

نقطة البداية الرئيسية للنظام
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """نقطة البداية الرئيسية"""
    print("مرحباً بك في نظام متابعة الوارد والصادر (IOTS)")
    print("Welcome to In and Out Tracking System (IOTS)")
    
    # التحقق من وجود المتطلبات
    try:
        import flask
        import PyQt6
        import mysql.connector
        import sqlite3
        print("✓ جميع المتطلبات متوفرة")
    except ImportError as e:
        print(f"✗ خطأ في المتطلبات: {e}")
        print("يرجى تثبيت المتطلبات باستخدام: pip install -r requirements.txt")
        return
    
    # عرض خيارات التشغيل
    print("\nخيارات التشغيل:")
    print("1. تشغيل تطبيق الويب (Web Application)")
    print("2. تشغيل تطبيق سطح المكتب (Desktop Application)")
    print("3. إعداد قاعدة البيانات (Database Setup)")
    print("4. خروج (Exit)")
    
    choice = input("\nاختر رقم الخيار: ")
    
    if choice == "1":
        from web_app.app import create_app
        app = create_app()
        print("تشغيل تطبيق الويب على http://localhost:5000")
        app.run(host='0.0.0.0', port=5000, debug=True)
    
    elif choice == "2":
        from desktop_app.main import run_desktop_app
        run_desktop_app()
    
    elif choice == "3":
        from config.database import setup_database
        setup_database()
    
    elif choice == "4":
        print("شكراً لاستخدام النظام!")
        sys.exit(0)
    
    else:
        print("خيار غير صحيح!")
        main()

if __name__ == "__main__":
    main()
