# -*- coding: utf-8 -*-
"""
نماذج الجداول المساعدة
Enumeration Models

يحتوي على فئات الجداول المساعدة مثل أنواع التأشيرات والحالات
"""

from config.database import DatabaseConnection

class BaseEnum:
    """الفئة الأساسية للجداول المساعدة"""
    
    def __init__(self, id=None, name=None, description=None, is_active=True):
        self.id = id
        self.name = name
        self.description = description
        self.is_active = is_active
        self.created_at = None
    
    def to_dict(self):
        """تحويل الكائن إلى dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class VisaType(BaseEnum):
    """فئة أنواع التأشيرات"""
    
    def __init__(self, id=None, visa_type=None, description=None, is_active=True):
        super().__init__(id, visa_type, description, is_active)
        self.visa_type = visa_type

class ReceivedFromSource(BaseEnum):
    """فئة مصادر الورود"""
    
    def __init__(self, id=None, received_from=None, contact_info=None, is_active=True):
        super().__init__(id, received_from, None, is_active)
        self.received_from = received_from
        self.contact_info = contact_info
    
    def to_dict(self):
        result = super().to_dict()
        result['contact_info'] = self.contact_info
        return result

class ActionTaken(BaseEnum):
    """فئة الإجراءات المتخذة"""
    
    def __init__(self, id=None, action_taken=None, description=None, is_active=True):
        super().__init__(id, action_taken, description, is_active)
        self.action_taken = action_taken

class RequestStatus(BaseEnum):
    """فئة حالات الطلبات"""
    
    def __init__(self, id=None, request_status=None, status_color='#007bff', 
                 description=None, is_active=True):
        super().__init__(id, request_status, description, is_active)
        self.request_status = request_status
        self.status_color = status_color
    
    def to_dict(self):
        result = super().to_dict()
        result['status_color'] = self.status_color
        return result

class EnumManager:
    """مدير الجداول المساعدة"""
    
    def __init__(self, use_mysql=True):
        self.db_conn = DatabaseConnection()
        self.use_mysql = use_mysql
    
    def _get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        if self.use_mysql:
            return self.db_conn.connect_mysql()
        else:
            return self.db_conn.connect_sqlite()
    
    def get_visa_types(self, active_only=True):
        """الحصول على أنواع التأشيرات"""
        try:
            conn = self._get_connection()
            if not conn:
                return []
            
            cursor = conn.cursor()
            
            query = "SELECT * FROM visa_types"
            if active_only:
                query += " WHERE is_active = TRUE" if self.use_mysql else " WHERE is_active = 1"
            query += " ORDER BY visa_type"
            
            cursor.execute(query)
            rows = cursor.fetchall()
            
            visa_types = []
            for row in rows:
                if self.use_mysql:
                    visa_type = VisaType(
                        id=row[0],
                        visa_type=row[1],
                        description=row[2],
                        is_active=row[3]
                    )
                    visa_type.created_at = row[4]
                else:
                    visa_type = VisaType(
                        id=row['id'],
                        visa_type=row['visa_type'],
                        description=row['description'],
                        is_active=row['is_active']
                    )
                    visa_type.created_at = row['created_at']
                
                visa_types.append(visa_type)
            
            cursor.close()
            conn.close()
            return visa_types
            
        except Exception as e:
            print(f"خطأ في الحصول على أنواع التأشيرات: {e}")
            return []
    
    def get_received_from_sources(self, active_only=True):
        """الحصول على مصادر الورود"""
        try:
            conn = self._get_connection()
            if not conn:
                return []
            
            cursor = conn.cursor()
            
            query = "SELECT * FROM received_from_sources"
            if active_only:
                query += " WHERE is_active = TRUE" if self.use_mysql else " WHERE is_active = 1"
            query += " ORDER BY received_from"
            
            cursor.execute(query)
            rows = cursor.fetchall()
            
            sources = []
            for row in rows:
                if self.use_mysql:
                    source = ReceivedFromSource(
                        id=row[0],
                        received_from=row[1],
                        contact_info=row[2],
                        is_active=row[3]
                    )
                    source.created_at = row[4]
                else:
                    source = ReceivedFromSource(
                        id=row['id'],
                        received_from=row['received_from'],
                        contact_info=row['contact_info'],
                        is_active=row['is_active']
                    )
                    source.created_at = row['created_at']
                
                sources.append(source)
            
            cursor.close()
            conn.close()
            return sources
            
        except Exception as e:
            print(f"خطأ في الحصول على مصادر الورود: {e}")
            return []
    
    def get_actions_taken(self, active_only=True):
        """الحصول على الإجراءات المتخذة"""
        try:
            conn = self._get_connection()
            if not conn:
                return []
            
            cursor = conn.cursor()
            
            query = "SELECT * FROM actions_taken"
            if active_only:
                query += " WHERE is_active = TRUE" if self.use_mysql else " WHERE is_active = 1"
            query += " ORDER BY action_taken"
            
            cursor.execute(query)
            rows = cursor.fetchall()
            
            actions = []
            for row in rows:
                if self.use_mysql:
                    action = ActionTaken(
                        id=row[0],
                        action_taken=row[1],
                        description=row[2],
                        is_active=row[3]
                    )
                    action.created_at = row[4]
                else:
                    action = ActionTaken(
                        id=row['id'],
                        action_taken=row['action_taken'],
                        description=row['description'],
                        is_active=row['is_active']
                    )
                    action.created_at = row['created_at']
                
                actions.append(action)
            
            cursor.close()
            conn.close()
            return actions
            
        except Exception as e:
            print(f"خطأ في الحصول على الإجراءات المتخذة: {e}")
            return []
    
    def get_request_statuses(self, active_only=True):
        """الحصول على حالات الطلبات"""
        try:
            conn = self._get_connection()
            if not conn:
                return []
            
            cursor = conn.cursor()
            
            query = "SELECT * FROM request_statuses"
            if active_only:
                query += " WHERE is_active = TRUE" if self.use_mysql else " WHERE is_active = 1"
            query += " ORDER BY request_status"
            
            cursor.execute(query)
            rows = cursor.fetchall()
            
            statuses = []
            for row in rows:
                if self.use_mysql:
                    status = RequestStatus(
                        id=row[0],
                        request_status=row[1],
                        status_color=row[2],
                        description=row[3],
                        is_active=row[4]
                    )
                    status.created_at = row[5]
                else:
                    status = RequestStatus(
                        id=row['id'],
                        request_status=row['request_status'],
                        status_color=row['status_color'],
                        description=row['description'],
                        is_active=row['is_active']
                    )
                    status.created_at = row['created_at']
                
                statuses.append(status)
            
            cursor.close()
            conn.close()
            return statuses
            
        except Exception as e:
            print(f"خطأ في الحصول على حالات الطلبات: {e}")
            return []
    
    def add_visa_type(self, visa_type, description=None):
        """إضافة نوع تأشيرة جديد"""
        try:
            conn = self._get_connection()
            if not conn:
                return False, "فشل الاتصال بقاعدة البيانات"
            
            cursor = conn.cursor()
            
            if self.use_mysql:
                query = "INSERT INTO visa_types (visa_type, description) VALUES (%s, %s)"
                cursor.execute(query, (visa_type, description))
            else:
                query = "INSERT INTO visa_types (visa_type, description) VALUES (?, ?)"
                cursor.execute(query, (visa_type, description))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            return True, "تم إضافة نوع التأشيرة بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إضافة نوع التأشيرة: {str(e)}"
    
    def add_received_from_source(self, received_from, contact_info=None):
        """إضافة مصدر ورود جديد"""
        try:
            conn = self._get_connection()
            if not conn:
                return False, "فشل الاتصال بقاعدة البيانات"
            
            cursor = conn.cursor()
            
            if self.use_mysql:
                query = "INSERT INTO received_from_sources (received_from, contact_info) VALUES (%s, %s)"
                cursor.execute(query, (received_from, contact_info))
            else:
                query = "INSERT INTO received_from_sources (received_from, contact_info) VALUES (?, ?)"
                cursor.execute(query, (received_from, contact_info))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            return True, "تم إضافة مصدر الورود بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إضافة مصدر الورود: {str(e)}"
    
    def add_action_taken(self, action_taken, description=None):
        """إضافة إجراء متخذ جديد"""
        try:
            conn = self._get_connection()
            if not conn:
                return False, "فشل الاتصال بقاعدة البيانات"
            
            cursor = conn.cursor()
            
            if self.use_mysql:
                query = "INSERT INTO actions_taken (action_taken, description) VALUES (%s, %s)"
                cursor.execute(query, (action_taken, description))
            else:
                query = "INSERT INTO actions_taken (action_taken, description) VALUES (?, ?)"
                cursor.execute(query, (action_taken, description))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            return True, "تم إضافة الإجراء المتخذ بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إضافة الإجراء المتخذ: {str(e)}"
    
    def add_request_status(self, request_status, status_color='#007bff', description=None):
        """إضافة حالة طلب جديدة"""
        try:
            conn = self._get_connection()
            if not conn:
                return False, "فشل الاتصال بقاعدة البيانات"
            
            cursor = conn.cursor()
            
            if self.use_mysql:
                query = "INSERT INTO request_statuses (request_status, status_color, description) VALUES (%s, %s, %s)"
                cursor.execute(query, (request_status, status_color, description))
            else:
                query = "INSERT INTO request_statuses (request_status, status_color, description) VALUES (?, ?, ?)"
                cursor.execute(query, (request_status, status_color, description))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            return True, "تم إضافة حالة الطلب بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إضافة حالة الطلب: {str(e)}"
