# -*- coding: utf-8 -*-
"""
نموذج المعاملات
Transaction Model

يحتوي على فئة المعاملة وعمليات إدارة المعاملات
"""

from datetime import datetime, date
from config.database import DatabaseConnection

class Transaction:
    """فئة المعاملة"""
    
    def __init__(self, id=None, head_incoming_no=None, head_incoming_date=None,
                 subject=None, researcher_notes=None, priority='medium',
                 due_date=None, user_id=None, researcher_1_id=None,
                 researcher_2_id=None, visa_type_id=None, received_from_id=None,
                 action_taken_id=None, request_status_id=None):
        self.id = id
        self.head_incoming_no = head_incoming_no
        self.head_incoming_date = head_incoming_date
        self.subject = subject
        self.researcher_notes = researcher_notes
        self.priority = priority
        self.due_date = due_date
        self.user_id = user_id
        self.researcher_1_id = researcher_1_id
        self.researcher_2_id = researcher_2_id
        self.visa_type_id = visa_type_id
        self.received_from_id = received_from_id
        self.action_taken_id = action_taken_id
        self.request_status_id = request_status_id
        self.created_at = None
        self.updated_at = None
        self.completed_at = None
    
    def to_dict(self):
        """تحويل المعاملة إلى dictionary"""
        return {
            'id': self.id,
            'head_incoming_no': self.head_incoming_no,
            'head_incoming_date': self.head_incoming_date.isoformat() if self.head_incoming_date else None,
            'subject': self.subject,
            'researcher_notes': self.researcher_notes,
            'priority': self.priority,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'user_id': self.user_id,
            'researcher_1_id': self.researcher_1_id,
            'researcher_2_id': self.researcher_2_id,
            'visa_type_id': self.visa_type_id,
            'received_from_id': self.received_from_id,
            'action_taken_id': self.action_taken_id,
            'request_status_id': self.request_status_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }

class TransactionManager:
    """مدير المعاملات"""
    
    def __init__(self, use_mysql=True):
        self.db_conn = DatabaseConnection()
        self.use_mysql = use_mysql
    
    def _get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        if self.use_mysql:
            return self.db_conn.connect_mysql()
        else:
            return self.db_conn.connect_sqlite()
    
    def create_transaction(self, transaction_data):
        """إنشاء معاملة جديدة"""
        try:
            conn = self._get_connection()
            if not conn:
                return False, "فشل الاتصال بقاعدة البيانات"
            
            cursor = conn.cursor()
            
            if self.use_mysql:
                query = """
                INSERT INTO transactions (
                    head_incoming_no, head_incoming_date, subject, researcher_notes,
                    priority, due_date, user_id, researcher_1_id, researcher_2_id,
                    visa_type_id, received_from_id, action_taken_id, request_status_id
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(query, (
                    transaction_data['head_incoming_no'],
                    transaction_data['head_incoming_date'],
                    transaction_data['subject'],
                    transaction_data.get('researcher_notes'),
                    transaction_data.get('priority', 'medium'),
                    transaction_data.get('due_date'),
                    transaction_data['user_id'],
                    transaction_data.get('researcher_1_id'),
                    transaction_data.get('researcher_2_id'),
                    transaction_data.get('visa_type_id'),
                    transaction_data.get('received_from_id'),
                    transaction_data.get('action_taken_id'),
                    transaction_data.get('request_status_id')
                ))
            else:
                query = """
                INSERT INTO transactions (
                    head_incoming_no, head_incoming_date, subject, researcher_notes,
                    priority, due_date, user_id, researcher_1_id, researcher_2_id,
                    visa_type_id, received_from_id, action_taken_id, request_status_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                cursor.execute(query, (
                    transaction_data['head_incoming_no'],
                    transaction_data['head_incoming_date'],
                    transaction_data['subject'],
                    transaction_data.get('researcher_notes'),
                    transaction_data.get('priority', 'medium'),
                    transaction_data.get('due_date'),
                    transaction_data['user_id'],
                    transaction_data.get('researcher_1_id'),
                    transaction_data.get('researcher_2_id'),
                    transaction_data.get('visa_type_id'),
                    transaction_data.get('received_from_id'),
                    transaction_data.get('action_taken_id'),
                    transaction_data.get('request_status_id')
                ))
            
            conn.commit()
            transaction_id = cursor.lastrowid
            cursor.close()
            conn.close()
            
            return True, f"تم إنشاء المعاملة بنجاح برقم {transaction_id}"
            
        except Exception as e:
            return False, f"خطأ في إنشاء المعاملة: {str(e)}"
    
    def get_transaction_by_id(self, transaction_id):
        """الحصول على معاملة بالمعرف"""
        try:
            conn = self._get_connection()
            if not conn:
                return None
            
            cursor = conn.cursor()
            
            if self.use_mysql:
                cursor.execute("SELECT * FROM transactions WHERE id = %s", (transaction_id,))
            else:
                cursor.execute("SELECT * FROM transactions WHERE id = ?", (transaction_id,))
            
            row = cursor.fetchone()
            cursor.close()
            conn.close()
            
            if row:
                return self._row_to_transaction(row)
            return None
            
        except Exception as e:
            print(f"خطأ في الحصول على المعاملة: {e}")
            return None
    
    def get_all_transactions(self, limit=None, offset=0):
        """الحصول على جميع المعاملات"""
        try:
            conn = self._get_connection()
            if not conn:
                return []
            
            cursor = conn.cursor()
            
            query = "SELECT * FROM transactions ORDER BY created_at DESC"
            if limit:
                if self.use_mysql:
                    query += f" LIMIT {limit} OFFSET {offset}"
                else:
                    query += f" LIMIT {limit} OFFSET {offset}"
            
            cursor.execute(query)
            rows = cursor.fetchall()
            
            transactions = []
            for row in rows:
                transactions.append(self._row_to_transaction(row))
            
            cursor.close()
            conn.close()
            return transactions
            
        except Exception as e:
            print(f"خطأ في الحصول على المعاملات: {e}")
            return []
    
    def get_transactions_by_researcher(self, researcher_id):
        """الحصول على معاملات باحث معين"""
        try:
            conn = self._get_connection()
            if not conn:
                return []
            
            cursor = conn.cursor()
            
            if self.use_mysql:
                query = """
                SELECT * FROM transactions 
                WHERE researcher_1_id = %s OR researcher_2_id = %s
                ORDER BY created_at DESC
                """
                cursor.execute(query, (researcher_id, researcher_id))
            else:
                query = """
                SELECT * FROM transactions 
                WHERE researcher_1_id = ? OR researcher_2_id = ?
                ORDER BY created_at DESC
                """
                cursor.execute(query, (researcher_id, researcher_id))
            
            rows = cursor.fetchall()
            
            transactions = []
            for row in rows:
                transactions.append(self._row_to_transaction(row))
            
            cursor.close()
            conn.close()
            return transactions
            
        except Exception as e:
            print(f"خطأ في الحصول على معاملات الباحث: {e}")
            return []
    
    def search_transactions(self, search_params):
        """البحث في المعاملات"""
        try:
            conn = self._get_connection()
            if not conn:
                return []
            
            cursor = conn.cursor()
            
            # بناء استعلام البحث
            where_conditions = []
            params = []
            
            if search_params.get('head_incoming_no'):
                where_conditions.append("head_incoming_no LIKE %s" if self.use_mysql else "head_incoming_no LIKE ?")
                params.append(f"%{search_params['head_incoming_no']}%")
            
            if search_params.get('subject'):
                where_conditions.append("subject LIKE %s" if self.use_mysql else "subject LIKE ?")
                params.append(f"%{search_params['subject']}%")
            
            if search_params.get('date_from'):
                where_conditions.append("head_incoming_date >= %s" if self.use_mysql else "head_incoming_date >= ?")
                params.append(search_params['date_from'])
            
            if search_params.get('date_to'):
                where_conditions.append("head_incoming_date <= %s" if self.use_mysql else "head_incoming_date <= ?")
                params.append(search_params['date_to'])
            
            if search_params.get('researcher_id'):
                where_conditions.append("(researcher_1_id = %s OR researcher_2_id = %s)" if self.use_mysql else "(researcher_1_id = ? OR researcher_2_id = ?)")
                params.extend([search_params['researcher_id'], search_params['researcher_id']])
            
            if search_params.get('status_id'):
                where_conditions.append("request_status_id = %s" if self.use_mysql else "request_status_id = ?")
                params.append(search_params['status_id'])
            
            query = "SELECT * FROM transactions"
            if where_conditions:
                query += " WHERE " + " AND ".join(where_conditions)
            query += " ORDER BY created_at DESC"
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            transactions = []
            for row in rows:
                transactions.append(self._row_to_transaction(row))
            
            cursor.close()
            conn.close()
            return transactions
            
        except Exception as e:
            print(f"خطأ في البحث في المعاملات: {e}")
            return []
    
    def update_transaction(self, transaction_id, update_data):
        """تحديث معاملة"""
        try:
            conn = self._get_connection()
            if not conn:
                return False, "فشل الاتصال بقاعدة البيانات"
            
            cursor = conn.cursor()
            
            # بناء استعلام التحديث
            set_clauses = []
            params = []
            
            for field, value in update_data.items():
                if field in ['subject', 'researcher_notes', 'priority', 'due_date',
                           'researcher_1_id', 'researcher_2_id', 'visa_type_id',
                           'received_from_id', 'action_taken_id', 'request_status_id']:
                    set_clauses.append(f"{field} = %s" if self.use_mysql else f"{field} = ?")
                    params.append(value)
            
            if not set_clauses:
                return False, "لا توجد بيانات للتحديث"
            
            params.append(transaction_id)
            
            query = f"UPDATE transactions SET {', '.join(set_clauses)} WHERE id = %s" if self.use_mysql else f"UPDATE transactions SET {', '.join(set_clauses)} WHERE id = ?"
            
            cursor.execute(query, params)
            conn.commit()
            
            affected_rows = cursor.rowcount
            cursor.close()
            conn.close()
            
            if affected_rows > 0:
                return True, "تم تحديث المعاملة بنجاح"
            else:
                return False, "لم يتم العثور على المعاملة"
            
        except Exception as e:
            return False, f"خطأ في تحديث المعاملة: {str(e)}"
    
    def _row_to_transaction(self, row):
        """تحويل صف قاعدة البيانات إلى كائن معاملة"""
        if self.use_mysql:
            transaction = Transaction(
                id=row[0],
                head_incoming_no=row[1],
                head_incoming_date=row[2],
                subject=row[3],
                researcher_notes=row[4],
                priority=row[5],
                due_date=row[6],
                user_id=row[7],
                researcher_1_id=row[8],
                researcher_2_id=row[9],
                visa_type_id=row[10],
                received_from_id=row[11],
                action_taken_id=row[12],
                request_status_id=row[13]
            )
            transaction.created_at = row[14]
            transaction.updated_at = row[15]
            transaction.completed_at = row[16]
        else:
            transaction = Transaction(
                id=row['id'],
                head_incoming_no=row['head_incoming_no'],
                head_incoming_date=row['head_incoming_date'],
                subject=row['subject'],
                researcher_notes=row['researcher_notes'],
                priority=row['priority'],
                due_date=row['due_date'],
                user_id=row['user_id'],
                researcher_1_id=row['researcher_1_id'],
                researcher_2_id=row['researcher_2_id'],
                visa_type_id=row['visa_type_id'],
                received_from_id=row['received_from_id'],
                action_taken_id=row['action_taken_id'],
                request_status_id=row['request_status_id']
            )
            transaction.created_at = row['created_at']
            transaction.updated_at = row['updated_at']
            transaction.completed_at = row['completed_at']
        
        return transaction
