# -*- coding: utf-8 -*-
"""
نموذج المستخدم
User Model

يحتوي على فئة المستخدم وعمليات إدارة المستخدمين
"""

from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
import mysql.connector
import sqlite3
from config.database import DatabaseConnection

class User(UserMixin):
    """فئة المستخدم"""
    
    def __init__(self, user_id=None, user_name=None, full_name=None, 
                 email=None, phone=None, permission='user', is_active=True):
        self.user_id = user_id
        self.user_name = user_name
        self.full_name = full_name
        self.email = email
        self.phone = phone
        self.permission = permission
        self.is_active = is_active
        self.created_at = None
        self.updated_at = None
        self.last_login = None
    
    def get_id(self):
        """إرجاع معرف المستخدم للـ Flask-Login"""
        return str(self.user_id)
    
    def is_admin(self):
        """التحقق من كون المستخدم مدير"""
        return self.permission == 'admin'
    
    def set_password(self, password):
        """تشفير وحفظ كلمة المرور"""
        return generate_password_hash(password)
    
    def check_password(self, password, hashed_password):
        """التحقق من كلمة المرور"""
        return check_password_hash(hashed_password, password)
    
    def to_dict(self):
        """تحويل المستخدم إلى dictionary"""
        return {
            'user_id': self.user_id,
            'user_name': self.user_name,
            'full_name': self.full_name,
            'email': self.email,
            'phone': self.phone,
            'permission': self.permission,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

class UserManager:
    """مدير المستخدمين"""
    
    def __init__(self, use_mysql=True):
        self.db_conn = DatabaseConnection()
        self.use_mysql = use_mysql
    
    def _get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        if self.use_mysql:
            return self.db_conn.connect_mysql()
        else:
            return self.db_conn.connect_sqlite()
    
    def create_user(self, user_name, password, full_name, email=None, 
                   phone=None, permission='user'):
        """إنشاء مستخدم جديد"""
        try:
            conn = self._get_connection()
            if not conn:
                return False, "فشل الاتصال بقاعدة البيانات"
            
            cursor = conn.cursor()
            hashed_password = generate_password_hash(password)
            
            if self.use_mysql:
                query = """
                INSERT INTO users (user_name, user_pass, full_name, email, phone, permission)
                VALUES (%s, %s, %s, %s, %s, %s)
                """
                cursor.execute(query, (user_name, hashed_password, full_name, 
                                     email, phone, permission))
            else:
                query = """
                INSERT INTO users (user_name, user_pass, full_name, email, phone, permission)
                VALUES (?, ?, ?, ?, ?, ?)
                """
                cursor.execute(query, (user_name, hashed_password, full_name, 
                                     email, phone, permission))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            return True, "تم إنشاء المستخدم بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إنشاء المستخدم: {str(e)}"
    
    def get_user_by_id(self, user_id):
        """الحصول على مستخدم بالمعرف"""
        try:
            conn = self._get_connection()
            if not conn:
                return None
            
            cursor = conn.cursor()
            
            if self.use_mysql:
                cursor.execute("SELECT * FROM users WHERE user_id = %s", (user_id,))
            else:
                cursor.execute("SELECT * FROM users WHERE user_id = ?", (user_id,))
            
            row = cursor.fetchone()
            cursor.close()
            conn.close()
            
            if row:
                return self._row_to_user(row)
            return None
            
        except Exception as e:
            print(f"خطأ في الحصول على المستخدم: {e}")
            return None
    
    def get_user_by_username(self, user_name):
        """الحصول على مستخدم باسم المستخدم"""
        try:
            conn = self._get_connection()
            if not conn:
                return None
            
            cursor = conn.cursor()
            
            if self.use_mysql:
                cursor.execute("SELECT * FROM users WHERE user_name = %s", (user_name,))
            else:
                cursor.execute("SELECT * FROM users WHERE user_name = ?", (user_name,))
            
            row = cursor.fetchone()
            cursor.close()
            conn.close()
            
            if row:
                return self._row_to_user(row)
            return None
            
        except Exception as e:
            print(f"خطأ في الحصول على المستخدم: {e}")
            return None
    
    def authenticate_user(self, user_name, password):
        """التحقق من صحة بيانات المستخدم"""
        try:
            conn = self._get_connection()
            if not conn:
                return None, "فشل الاتصال بقاعدة البيانات"
            
            cursor = conn.cursor()
            
            if self.use_mysql:
                cursor.execute(
                    "SELECT * FROM users WHERE user_name = %s AND is_active = TRUE", 
                    (user_name,)
                )
            else:
                cursor.execute(
                    "SELECT * FROM users WHERE user_name = ? AND is_active = 1", 
                    (user_name,)
                )
            
            row = cursor.fetchone()
            
            if row:
                user = self._row_to_user(row)
                # التحقق من كلمة المرور
                if self.use_mysql:
                    stored_password = row[2]  # user_pass column
                else:
                    stored_password = row['user_pass']
                
                if check_password_hash(stored_password, password):
                    # تحديث آخر تسجيل دخول
                    self._update_last_login(user.user_id)
                    cursor.close()
                    conn.close()
                    return user, "تم تسجيل الدخول بنجاح"
            
            cursor.close()
            conn.close()
            return None, "اسم المستخدم أو كلمة المرور غير صحيحة"
            
        except Exception as e:
            return None, f"خطأ في التحقق من المستخدم: {str(e)}"
    
    def get_all_users(self):
        """الحصول على جميع المستخدمين"""
        try:
            conn = self._get_connection()
            if not conn:
                return []
            
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM users ORDER BY full_name")
            rows = cursor.fetchall()
            
            users = []
            for row in rows:
                users.append(self._row_to_user(row))
            
            cursor.close()
            conn.close()
            return users
            
        except Exception as e:
            print(f"خطأ في الحصول على المستخدمين: {e}")
            return []
    
    def _row_to_user(self, row):
        """تحويل صف قاعدة البيانات إلى كائن مستخدم"""
        if self.use_mysql:
            user = User(
                user_id=row[0],
                user_name=row[1],
                full_name=row[3],
                email=row[4],
                phone=row[5],
                permission=row[6],
                is_active=row[7]
            )
            user.created_at = row[8]
            user.updated_at = row[9]
            user.last_login = row[10]
        else:
            user = User(
                user_id=row['user_id'],
                user_name=row['user_name'],
                full_name=row['full_name'],
                email=row['email'],
                phone=row['phone'],
                permission=row['permission'],
                is_active=row['is_active']
            )
            user.created_at = row['created_at']
            user.updated_at = row['updated_at']
            user.last_login = row['last_login']
        
        return user
    
    def _update_last_login(self, user_id):
        """تحديث آخر تسجيل دخول"""
        try:
            conn = self._get_connection()
            if not conn:
                return
            
            cursor = conn.cursor()
            
            if self.use_mysql:
                cursor.execute(
                    "UPDATE users SET last_login = NOW() WHERE user_id = %s", 
                    (user_id,)
                )
            else:
                cursor.execute(
                    "UPDATE users SET last_login = datetime('now') WHERE user_id = ?", 
                    (user_id,)
                )
            
            conn.commit()
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تحديث آخر تسجيل دخول: {e}")

# دالة مساعدة لتحميل المستخدم للـ Flask-Login
def load_user(user_id):
    """تحميل المستخدم للـ Flask-Login"""
    user_manager = UserManager()
    return user_manager.get_user_by_id(int(user_id))
