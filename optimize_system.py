#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحسين أداء النظام
System Performance Optimization

يحتوي على أدوات تحسين أداء النظام وتنظيف قاعدة البيانات
"""

import os
import sys
import time
import sqlite3
from pathlib import Path
from datetime import datetime, timedelta

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.database import DatabaseConnection

class SystemOptimizer:
    """محسن أداء النظام"""
    
    def __init__(self):
        self.db_conn = DatabaseConnection()
        self.optimization_log = []
    
    def log_action(self, action, result, duration=None):
        """تسجيل إجراء التحسين"""
        log_entry = {
            'timestamp': datetime.now(),
            'action': action,
            'result': result,
            'duration': duration
        }
        self.optimization_log.append(log_entry)
        
        status = "✓" if result else "✗"
        duration_text = f" ({duration:.3f}s)" if duration else ""
        print(f"{status} {action}{duration_text}")
    
    def optimize_sqlite_database(self):
        """تحسين قاعدة بيانات SQLite"""
        print("تحسين قاعدة بيانات SQLite...")
        
        try:
            start_time = time.time()
            conn = self.db_conn.connect_sqlite()
            
            if not conn:
                self.log_action("الاتصال بـ SQLite", False)
                return False
            
            cursor = conn.cursor()
            
            # تحليل قاعدة البيانات
            cursor.execute("ANALYZE")
            
            # إعادة بناء الفهارس
            cursor.execute("REINDEX")
            
            # ضغط قاعدة البيانات
            cursor.execute("VACUUM")
            
            # تحسين إعدادات الأداء
            optimizations = [
                "PRAGMA journal_mode = WAL",
                "PRAGMA synchronous = NORMAL", 
                "PRAGMA cache_size = 10000",
                "PRAGMA temp_store = MEMORY",
                "PRAGMA mmap_size = 268435456"  # 256MB
            ]
            
            for optimization in optimizations:
                cursor.execute(optimization)
            
            conn.commit()
            cursor.close()
            conn.close()
            
            duration = time.time() - start_time
            self.log_action("تحسين قاعدة بيانات SQLite", True, duration)
            return True
            
        except Exception as e:
            self.log_action(f"تحسين SQLite - خطأ: {e}", False)
            return False
    
    def optimize_mysql_database(self):
        """تحسين قاعدة بيانات MySQL"""
        print("تحسين قاعدة بيانات MySQL...")
        
        try:
            start_time = time.time()
            conn = self.db_conn.connect_mysql()
            
            if not conn:
                self.log_action("الاتصال بـ MySQL", False)
                return False
            
            cursor = conn.cursor()
            
            # الحصول على قائمة الجداول
            cursor.execute("SHOW TABLES")
            tables = [table[0] for table in cursor.fetchall()]
            
            # تحسين كل جدول
            for table in tables:
                try:
                    cursor.execute(f"OPTIMIZE TABLE {table}")
                    cursor.execute(f"ANALYZE TABLE {table}")
                except Exception as e:
                    print(f"تحذير: فشل تحسين الجدول {table}: {e}")
            
            cursor.close()
            conn.close()
            
            duration = time.time() - start_time
            self.log_action("تحسين قاعدة بيانات MySQL", True, duration)
            return True
            
        except Exception as e:
            self.log_action(f"تحسين MySQL - خطأ: {e}", False)
            return False
    
    def clean_old_logs(self, days_to_keep=30):
        """تنظيف السجلات القديمة"""
        print(f"تنظيف السجلات الأقدم من {days_to_keep} يوم...")
        
        try:
            start_time = time.time()
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            # تنظيف SQLite
            sqlite_conn = self.db_conn.connect_sqlite()
            if sqlite_conn:
                cursor = sqlite_conn.cursor()
                
                # حذف سجلات المعاملات القديمة (إذا كان هناك جدول للسجلات)
                try:
                    cursor.execute("""
                        DELETE FROM transaction_logs 
                        WHERE created_at < ?
                    """, (cutoff_date.isoformat(),))
                    
                    deleted_count = cursor.rowcount
                    sqlite_conn.commit()
                    
                    if deleted_count > 0:
                        print(f"  تم حذف {deleted_count} سجل من SQLite")
                        
                except sqlite3.OperationalError:
                    # الجدول غير موجود
                    pass
                
                cursor.close()
                sqlite_conn.close()
            
            # تنظيف MySQL
            mysql_conn = self.db_conn.connect_mysql()
            if mysql_conn:
                cursor = mysql_conn.cursor()
                
                try:
                    cursor.execute("""
                        DELETE FROM transaction_logs 
                        WHERE created_at < %s
                    """, (cutoff_date,))
                    
                    deleted_count = cursor.rowcount
                    mysql_conn.commit()
                    
                    if deleted_count > 0:
                        print(f"  تم حذف {deleted_count} سجل من MySQL")
                        
                except Exception:
                    # الجدول غير موجود أو خطأ آخر
                    pass
                
                cursor.close()
                mysql_conn.close()
            
            duration = time.time() - start_time
            self.log_action("تنظيف السجلات القديمة", True, duration)
            return True
            
        except Exception as e:
            self.log_action(f"تنظيف السجلات - خطأ: {e}", False)
            return False
    
    def clean_temp_files(self):
        """تنظيف الملفات المؤقتة"""
        print("تنظيف الملفات المؤقتة...")
        
        try:
            start_time = time.time()
            cleaned_count = 0
            
            # مجلدات الملفات المؤقتة
            temp_dirs = [
                Path("reports"),
                Path("temp"),
                Path("__pycache__"),
                Path("build"),
                Path("dist")
            ]
            
            for temp_dir in temp_dirs:
                if temp_dir.exists():
                    if temp_dir.name == "reports":
                        # حذف التقارير الأقدم من 7 أيام
                        cutoff_time = time.time() - (7 * 24 * 60 * 60)
                        for file_path in temp_dir.glob("*"):
                            if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                                file_path.unlink()
                                cleaned_count += 1
                    elif temp_dir.name in ["__pycache__", "build", "dist"]:
                        # حذف مجلدات البناء
                        import shutil
                        shutil.rmtree(temp_dir)
                        cleaned_count += 1
            
            # تنظيف ملفات Python المؤقتة
            for pyc_file in Path(".").rglob("*.pyc"):
                pyc_file.unlink()
                cleaned_count += 1
            
            duration = time.time() - start_time
            self.log_action(f"تنظيف {cleaned_count} ملف مؤقت", True, duration)
            return True
            
        except Exception as e:
            self.log_action(f"تنظيف الملفات المؤقتة - خطأ: {e}", False)
            return False
    
    def check_database_integrity(self):
        """فحص سلامة قاعدة البيانات"""
        print("فحص سلامة قاعدة البيانات...")
        
        try:
            start_time = time.time()
            issues_found = 0
            
            # فحص SQLite
            sqlite_conn = self.db_conn.connect_sqlite()
            if sqlite_conn:
                cursor = sqlite_conn.cursor()
                
                # فحص سلامة قاعدة البيانات
                cursor.execute("PRAGMA integrity_check")
                result = cursor.fetchone()
                
                if result[0] != "ok":
                    issues_found += 1
                    print(f"  مشكلة في SQLite: {result[0]}")
                
                cursor.close()
                sqlite_conn.close()
            
            # فحص MySQL
            mysql_conn = self.db_conn.connect_mysql()
            if mysql_conn:
                cursor = mysql_conn.cursor()
                
                # فحص الجداول
                cursor.execute("SHOW TABLES")
                tables = [table[0] for table in cursor.fetchall()]
                
                for table in tables:
                    cursor.execute(f"CHECK TABLE {table}")
                    result = cursor.fetchone()
                    
                    if result and result[3] != "OK":
                        issues_found += 1
                        print(f"  مشكلة في جدول {table}: {result[3]}")
                
                cursor.close()
                mysql_conn.close()
            
            duration = time.time() - start_time
            
            if issues_found == 0:
                self.log_action("فحص سلامة قاعدة البيانات - لا توجد مشاكل", True, duration)
            else:
                self.log_action(f"فحص سلامة قاعدة البيانات - وجد {issues_found} مشكلة", False, duration)
            
            return issues_found == 0
            
        except Exception as e:
            self.log_action(f"فحص سلامة قاعدة البيانات - خطأ: {e}", False)
            return False
    
    def update_statistics(self):
        """تحديث إحصائيات قاعدة البيانات"""
        print("تحديث إحصائيات قاعدة البيانات...")
        
        try:
            start_time = time.time()
            
            # تحديث إحصائيات SQLite
            sqlite_conn = self.db_conn.connect_sqlite()
            if sqlite_conn:
                cursor = sqlite_conn.cursor()
                cursor.execute("ANALYZE")
                cursor.close()
                sqlite_conn.close()
            
            # تحديث إحصائيات MySQL
            mysql_conn = self.db_conn.connect_mysql()
            if mysql_conn:
                cursor = mysql_conn.cursor()
                
                cursor.execute("SHOW TABLES")
                tables = [table[0] for table in cursor.fetchall()]
                
                for table in tables:
                    cursor.execute(f"ANALYZE TABLE {table}")
                
                cursor.close()
                mysql_conn.close()
            
            duration = time.time() - start_time
            self.log_action("تحديث إحصائيات قاعدة البيانات", True, duration)
            return True
            
        except Exception as e:
            self.log_action(f"تحديث الإحصائيات - خطأ: {e}", False)
            return False
    
    def generate_optimization_report(self):
        """إنشاء تقرير التحسين"""
        print("\nإنشاء تقرير التحسين...")
        
        report_content = f"""
# تقرير تحسين النظام
تاريخ التحسين: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## ملخص الإجراءات:
"""
        
        successful_actions = 0
        failed_actions = 0
        
        for log_entry in self.optimization_log:
            status = "✓" if log_entry['result'] else "✗"
            duration_text = f" ({log_entry['duration']:.3f}s)" if log_entry['duration'] else ""
            
            report_content += f"- {status} {log_entry['action']}{duration_text}\n"
            
            if log_entry['result']:
                successful_actions += 1
            else:
                failed_actions += 1
        
        report_content += f"""
## النتائج:
- إجراءات ناجحة: {successful_actions}
- إجراءات فاشلة: {failed_actions}
- معدل النجاح: {(successful_actions / len(self.optimization_log) * 100):.1f}%

## التوصيات:
"""
        
        if failed_actions > 0:
            report_content += "- يوجد إجراءات فاشلة تحتاج مراجعة\n"
        
        report_content += "- يُنصح بتشغيل التحسين دورياً (أسبوعياً)\n"
        report_content += "- مراقبة أداء النظام بعد التحسين\n"
        
        # حفظ التقرير
        reports_dir = Path("reports")
        reports_dir.mkdir(exist_ok=True)
        
        report_file = reports_dir / f"تقرير_التحسين_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"تم حفظ تقرير التحسين: {report_file}")
        return str(report_file)
    
    def run_full_optimization(self):
        """تشغيل تحسين شامل"""
        print("="*60)
        print("بدء التحسين الشامل للنظام")
        print("="*60)
        
        total_start_time = time.time()
        
        # تشغيل جميع إجراءات التحسين
        self.check_database_integrity()
        self.optimize_sqlite_database()
        self.optimize_mysql_database()
        self.update_statistics()
        self.clean_old_logs()
        self.clean_temp_files()
        
        total_duration = time.time() - total_start_time
        
        print("="*60)
        print(f"انتهى التحسين في {total_duration:.3f} ثانية")
        print("="*60)
        
        # إنشاء التقرير
        report_file = self.generate_optimization_report()
        
        return report_file

def main():
    """الدالة الرئيسية"""
    optimizer = SystemOptimizer()
    
    print("أداة تحسين نظام متابعة الوارد والصادر")
    print("="*50)
    
    while True:
        print("\nخيارات التحسين:")
        print("1. تحسين شامل")
        print("2. تحسين قاعدة البيانات فقط")
        print("3. تنظيف الملفات المؤقتة")
        print("4. فحص سلامة قاعدة البيانات")
        print("5. خروج")
        
        choice = input("\nاختر رقم الخيار: ").strip()
        
        if choice == "1":
            optimizer.run_full_optimization()
        
        elif choice == "2":
            optimizer.optimize_sqlite_database()
            optimizer.optimize_mysql_database()
        
        elif choice == "3":
            optimizer.clean_temp_files()
        
        elif choice == "4":
            optimizer.check_database_integrity()
        
        elif choice == "5":
            print("شكراً لاستخدام أداة التحسين!")
            break
        
        else:
            print("خيار غير صحيح!")

if __name__ == "__main__":
    main()
