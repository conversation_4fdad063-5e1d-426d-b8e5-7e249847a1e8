# متطلبات تطبيق الويب (Web Application Requirements)
Flask==2.3.3
Flask-Login==0.6.3
Flask-WTF==1.1.1
Flask-SQLAlchemy==3.0.5
Flask-Migrate==4.0.5
Werkzeug==2.3.7
WTForms==3.0.1
Jinja2==3.1.2

# متطلبات قاعدة البيانات (Database Requirements)
mysql-connector-python==8.1.0
SQLAlchemy==2.0.21
PyMySQL==1.1.0

# متطلبات تطبيق سطح المكتب (Desktop Application Requirements)
PyQt6==6.5.2
PyQt6-tools==6.4.2.3.3

# متطلبات التقارير والتصدير (Reports and Export Requirements)
openpyxl==3.1.2
xlsxwriter==3.1.9
reportlab==4.0.4
Pillow==10.0.1

# متطلبات الأمان والتشفير (Security and Encryption Requirements)
bcrypt==4.0.1
cryptography==41.0.4
passlib==1.7.4

# متطلبات المساعدة والأدوات (Helper and Utility Requirements)
python-dateutil==2.8.2
pytz==2023.3
requests==2.31.0
python-dotenv==1.0.0

# متطلبات التطوير والاختبار (Development and Testing Requirements)
pytest==7.4.2
pytest-flask==1.2.0
black==23.7.0
flake8==6.0.0

# متطلبات بناء التطبيق (Build Requirements)
PyInstaller==5.13.2
cx-Freeze==6.15.10
auto-py-to-exe==2.40.0

# متطلبات إضافية (Additional Requirements)
configparser==6.0.0
pathlib2==2.3.7
colorama==0.4.6
