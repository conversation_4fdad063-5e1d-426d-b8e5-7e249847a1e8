#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت إعداد النظام الشامل
Complete System Setup Script

يقوم بإعداد النظام كاملاً من الصفر
"""

import os
import sys
import subprocess
from pathlib import Path

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "=" * 60)
    print(f"  {title}")
    print("=" * 60)

def print_step(step_num, description):
    """طباعة خطوة مع ترقيم"""
    print(f"\n[{step_num}] {description}")
    print("-" * 40)

def run_command(command, description=""):
    """تشغيل أمر مع معالجة الأخطاء"""
    try:
        if description:
            print(f"تنفيذ: {description}")
        
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ تم بنجاح")
            if result.stdout.strip():
                print(f"النتيجة: {result.stdout.strip()}")
            return True
        else:
            print("✗ فشل")
            if result.stderr.strip():
                print(f"خطأ: {result.stderr.strip()}")
            return False
            
    except Exception as e:
        print(f"✗ خطأ في التنفيذ: {e}")
        return False

def check_python_version():
    """التحقق من إصدار Python"""
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✓ Python {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor}.{version.micro} - يتطلب Python 3.8 أو أحدث")
        return False

def install_requirements():
    """تثبيت المتطلبات"""
    print("تثبيت المتطلبات من requirements.txt...")
    
    if not Path("requirements.txt").exists():
        print("✗ ملف requirements.txt غير موجود")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "تثبيت المكتبات المطلوبة"
    )

def setup_database():
    """إعداد قاعدة البيانات"""
    print("إعداد قاعدة البيانات...")
    
    try:
        from config.database import setup_database
        setup_database()
        print("✓ تم إعداد قاعدة البيانات")
        return True
    except Exception as e:
        print(f"✗ فشل في إعداد قاعدة البيانات: {e}")
        return False

def download_assets():
    """تحميل الأصول المطلوبة"""
    print("تحميل الأصول المطلوبة (CSS, JS, إلخ)...")
    
    try:
        exec(open("download_assets.py").read())
        print("✓ تم تحميل الأصول")
        return True
    except Exception as e:
        print(f"✗ فشل في تحميل الأصول: {e}")
        return False

def test_web_app():
    """اختبار تطبيق الويب"""
    print("اختبار تطبيق الويب...")
    
    try:
        from web_app.app import create_app
        app = create_app()
        print("✓ تطبيق الويب جاهز")
        return True
    except Exception as e:
        print(f"✗ خطأ في تطبيق الويب: {e}")
        return False

def test_desktop_app():
    """اختبار تطبيق سطح المكتب"""
    print("اختبار تطبيق سطح المكتب...")
    
    try:
        import PyQt6
        from desktop_app.main import LoginDialog
        print("✓ تطبيق سطح المكتب جاهز")
        return True
    except ImportError:
        print("⚠ PyQt6 غير مثبت - تطبيق سطح المكتب غير متاح")
        return False
    except Exception as e:
        print(f"✗ خطأ في تطبيق سطح المكتب: {e}")
        return False

def create_shortcuts():
    """إنشاء اختصارات للتشغيل"""
    print("إنشاء اختصارات التشغيل...")
    
    # اختصار تشغيل تطبيق الويب
    web_script = """@echo off
chcp 65001 > nul
echo تشغيل تطبيق الويب...
echo يمكنك الوصول للتطبيق على: http://localhost:5000
echo للإيقاف اضغط Ctrl+C
python main.py
pause
"""
    
    with open("تشغيل_تطبيق_الويب.bat", "w", encoding="utf-8") as f:
        f.write(web_script)
    
    # اختصار تشغيل تطبيق سطح المكتب
    desktop_script = """@echo off
chcp 65001 > nul
echo تشغيل تطبيق سطح المكتب...
python -c "from desktop_app.main import run_desktop_app; run_desktop_app()"
pause
"""
    
    with open("تشغيل_تطبيق_سطح_المكتب.bat", "w", encoding="utf-8") as f:
        f.write(desktop_script)
    
    # اختصار إعداد قاعدة البيانات
    db_script = """@echo off
chcp 65001 > nul
echo إعداد قاعدة البيانات...
python -c "from config.database import setup_database; setup_database()"
pause
"""
    
    with open("إعداد_قاعدة_البيانات.bat", "w", encoding="utf-8") as f:
        f.write(db_script)
    
    print("✓ تم إنشاء اختصارات التشغيل")
    return True

def create_documentation():
    """إنشاء ملف التوثيق النهائي"""
    doc_content = """
# دليل الاستخدام السريع - نظام متابعة الوارد والصادر

## بيانات تسجيل الدخول الافتراضية:

### مدير النظام:
- اسم المستخدم: admin
- كلمة المرور: admin123

### مستخدم عادي:
- اسم المستخدم: user1  
- كلمة المرور: user123

## طرق التشغيل:

### 1. تشغيل تطبيق الويب:
- انقر مرتين على: تشغيل_تطبيق_الويب.bat
- أو استخدم الأمر: python main.py
- افتح المتصفح على: http://localhost:5000

### 2. تشغيل تطبيق سطح المكتب:
- انقر مرتين على: تشغيل_تطبيق_سطح_المكتب.bat
- أو استخدم الأمر: python desktop_app/main.py

### 3. إعداد قاعدة البيانات:
- انقر مرتين على: إعداد_قاعدة_البيانات.bat
- أو استخدم الأمر: python -c "from config.database import setup_database; setup_database()"

## الميزات الرئيسية:

### تطبيق الويب:
- لوحة تحكم تفاعلية
- إدارة المعاملات (إنشاء، تعديل، حذف)
- بحث وفلترة متقدمة
- نظام صلاحيات
- واجهة عربية متجاوبة

### تطبيق سطح المكتب:
- واجهة احترافية RTL
- نمط تشغيل مزدوج (متصل/غير متصل)
- مزامنة ذكية للبيانات
- إشعارات سطح المكتب

## إعدادات قاعدة البيانات:

يمكنك تعديل إعدادات قاعدة البيانات في ملف:
config/configuration.ini

## الدعم الفني:

للحصول على المساعدة:
- راجع ملف README.md للتوثيق الكامل
- تحقق من ملفات السجل في حالة وجود أخطاء

## ملاحظات مهمة:

1. تأكد من تشغيل خادم MySQL قبل استخدام النظام
2. يمكن استخدام تطبيق سطح المكتب بدون اتصال بالإنترنت
3. يتم حفظ البيانات المحلية في ملف: db/local.db
4. للمزامنة بين التطبيقين، استخدم زر "مزامنة" في تطبيق سطح المكتب

تاريخ الإعداد: {date}
الإصدار: 1.0.0
"""
    
    from datetime import datetime
    doc_content = doc_content.format(date=datetime.now().strftime("%Y-%m-%d %H:%M"))
    
    with open("دليل_الاستخدام_السريع.txt", "w", encoding="utf-8") as f:
        f.write(doc_content)
    
    print("✓ تم إنشاء دليل الاستخدام السريع")
    return True

def main():
    """الدالة الرئيسية"""
    print_header("إعداد نظام متابعة الوارد والصادر (IOTS)")
    print("مرحباً بك في معالج الإعداد الشامل")
    print("سيتم إعداد النظام كاملاً خطوة بخطوة...")
    
    # التحقق من Python
    print_step(1, "التحقق من إصدار Python")
    if not check_python_version():
        print("\n❌ يرجى تحديث Python إلى الإصدار 3.8 أو أحدث")
        return 1
    
    # تثبيت المتطلبات
    print_step(2, "تثبيت المتطلبات")
    if not install_requirements():
        print("\n⚠ تحذير: فشل في تثبيت بعض المتطلبات")
        response = input("هل تريد المتابعة؟ (y/n): ")
        if response.lower() != 'y':
            return 1
    
    # تحميل الأصول
    print_step(3, "تحميل الأصول المطلوبة")
    if not download_assets():
        print("\n⚠ تحذير: فشل في تحميل بعض الأصول")
    
    # إعداد قاعدة البيانات
    print_step(4, "إعداد قاعدة البيانات")
    if not setup_database():
        print("\n⚠ تحذير: فشل في إعداد قاعدة البيانات")
        print("يمكنك إعدادها لاحقاً باستخدام: python -c \"from config.database import setup_database; setup_database()\"")
    
    # اختبار التطبيقات
    print_step(5, "اختبار التطبيقات")
    web_ok = test_web_app()
    desktop_ok = test_desktop_app()
    
    # إنشاء الاختصارات
    print_step(6, "إنشاء اختصارات التشغيل")
    create_shortcuts()
    
    # إنشاء التوثيق
    print_step(7, "إنشاء دليل الاستخدام")
    create_documentation()
    
    # النتيجة النهائية
    print_header("تم الانتهاء من الإعداد!")
    
    print("📊 ملخص النتائج:")
    print(f"  • تطبيق الويب: {'✓ جاهز' if web_ok else '✗ يحتاج إصلاح'}")
    print(f"  • تطبيق سطح المكتب: {'✓ جاهز' if desktop_ok else '✗ يحتاج إصلاح'}")
    
    print("\n🚀 طرق التشغيل:")
    print("  • تطبيق الويب: تشغيل_تطبيق_الويب.bat")
    print("  • تطبيق سطح المكتب: تشغيل_تطبيق_سطح_المكتب.bat")
    
    print("\n📖 للمزيد من المعلومات:")
    print("  • دليل_الاستخدام_السريع.txt")
    print("  • README.md")
    
    print("\n🎉 مرحباً بك في نظام متابعة الوارد والصادر!")
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n⚠ تم إيقاف الإعداد بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
