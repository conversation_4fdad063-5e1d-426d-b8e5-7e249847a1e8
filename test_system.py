#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للنظام
Comprehensive System Testing

يقوم باختبار جميع مكونات النظام للتأكد من عملها بشكل صحيح
"""

import sys
import os
import unittest
from pathlib import Path
import tempfile
import shutil

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class TestDatabase(unittest.TestCase):
    """اختبار قاعدة البيانات"""
    
    def setUp(self):
        """إعداد الاختبار"""
        from config.database import DatabaseConnection
        self.db_conn = DatabaseConnection()
    
    def test_sqlite_connection(self):
        """اختبار الاتصال بـ SQLite"""
        conn = self.db_conn.connect_sqlite()
        self.assertIsNotNone(conn, "فشل الاتصال بـ SQLite")
        if conn:
            conn.close()
    
    def test_mysql_connection(self):
        """اختبار الاتصال بـ MySQL"""
        try:
            conn = self.db_conn.connect_mysql()
            if conn:
                self.assertTrue(conn.is_connected(), "فشل الاتصال بـ MySQL")
                conn.close()
            else:
                self.skipTest("MySQL غير متاح")
        except Exception as e:
            self.skipTest(f"MySQL غير متاح: {e}")

class TestUserModel(unittest.TestCase):
    """اختبار نموذج المستخدم"""
    
    def setUp(self):
        """إعداد الاختبار"""
        from models.user import UserManager
        self.user_manager = UserManager(use_mysql=False)
    
    def test_create_user(self):
        """اختبار إنشاء مستخدم"""
        success, message = self.user_manager.create_user(
            user_name="test_user",
            password="test_password",
            full_name="مستخدم تجريبي",
            email="<EMAIL>"
        )
        # قد ينجح أو يفشل حسب وجود المستخدم مسبقاً
        self.assertIsInstance(success, bool)
        self.assertIsInstance(message, str)
    
    def test_authenticate_user(self):
        """اختبار تسجيل الدخول"""
        # اختبار بيانات صحيحة (افتراضية)
        user, message = self.user_manager.authenticate_user("admin", "admin123")
        if user:
            self.assertIsNotNone(user.user_id)
            self.assertEqual(user.user_name, "admin")
        
        # اختبار بيانات خاطئة
        user, message = self.user_manager.authenticate_user("wrong_user", "wrong_pass")
        self.assertIsNone(user)

class TestTransactionModel(unittest.TestCase):
    """اختبار نموذج المعاملات"""
    
    def setUp(self):
        """إعداد الاختبار"""
        from models.transaction import TransactionManager
        self.transaction_manager = TransactionManager(use_mysql=False)
    
    def test_get_all_transactions(self):
        """اختبار الحصول على جميع المعاملات"""
        transactions = self.transaction_manager.get_all_transactions()
        self.assertIsInstance(transactions, list)
    
    def test_search_transactions(self):
        """اختبار البحث في المعاملات"""
        search_params = {'subject': 'test'}
        results = self.transaction_manager.search_transactions(search_params)
        self.assertIsInstance(results, list)

class TestEnumModel(unittest.TestCase):
    """اختبار النماذج المساعدة"""
    
    def setUp(self):
        """إعداد الاختبار"""
        from models.enums import EnumManager
        self.enum_manager = EnumManager(use_mysql=False)
    
    def test_get_visa_types(self):
        """اختبار الحصول على أنواع التأشيرات"""
        visa_types = self.enum_manager.get_visa_types()
        self.assertIsInstance(visa_types, list)
    
    def test_get_request_statuses(self):
        """اختبار الحصول على حالات الطلبات"""
        statuses = self.enum_manager.get_request_statuses()
        self.assertIsInstance(statuses, list)

class TestSyncSystem(unittest.TestCase):
    """اختبار نظام المزامنة"""
    
    def setUp(self):
        """إعداد الاختبار"""
        from config.sync import DataSynchronizer
        self.sync = DataSynchronizer()
    
    def test_create_sqlite_schema(self):
        """اختبار إنشاء مخطط SQLite"""
        success, message = self.sync.create_sqlite_schema()
        self.assertIsInstance(success, bool)
        self.assertIsInstance(message, str)
    
    def test_get_sync_status(self):
        """اختبار الحصول على حالة المزامنة"""
        status = self.sync.get_sync_status()
        self.assertIsInstance(status, dict)

class TestReportsSystem(unittest.TestCase):
    """اختبار نظام التقارير"""
    
    def setUp(self):
        """إعداد الاختبار"""
        try:
            from utils.reports import ReportGenerator
            self.report_generator = ReportGenerator(use_mysql=False)
            self.reports_available = True
        except ImportError:
            self.reports_available = False
    
    def test_generate_csv_report(self):
        """اختبار إنشاء تقرير CSV"""
        if not self.reports_available:
            self.skipTest("نظام التقارير غير متاح")
        
        try:
            file_path = self.report_generator.generate_transactions_report(format="csv")
            self.assertTrue(os.path.exists(file_path))
            # حذف الملف بعد الاختبار
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as e:
            self.fail(f"فشل في إنشاء تقرير CSV: {e}")

class TestWebApp(unittest.TestCase):
    """اختبار تطبيق الويب"""
    
    def setUp(self):
        """إعداد الاختبار"""
        try:
            from web_app.app import create_app
            self.app = create_app()
            self.app.config['TESTING'] = True
            self.client = self.app.test_client()
            self.web_available = True
        except ImportError:
            self.web_available = False
    
    def test_login_page(self):
        """اختبار صفحة تسجيل الدخول"""
        if not self.web_available:
            self.skipTest("تطبيق الويب غير متاح")
        
        response = self.client.get('/login')
        self.assertEqual(response.status_code, 200)
    
    def test_dashboard_redirect(self):
        """اختبار إعادة التوجيه للوحة التحكم"""
        if not self.web_available:
            self.skipTest("تطبيق الويب غير متاح")
        
        response = self.client.get('/')
        self.assertEqual(response.status_code, 302)  # إعادة توجيه

class TestDesktopApp(unittest.TestCase):
    """اختبار تطبيق سطح المكتب"""
    
    def setUp(self):
        """إعداد الاختبار"""
        try:
            import PyQt6
            from desktop_app.main import LoginDialog
            self.desktop_available = True
        except ImportError:
            self.desktop_available = False
    
    def test_import_desktop_modules(self):
        """اختبار استيراد وحدات تطبيق سطح المكتب"""
        if not self.desktop_available:
            self.skipTest("PyQt6 غير متاح")
        
        try:
            from desktop_app.main import MainWindow, LoginDialog
            self.assertTrue(True)  # نجح الاستيراد
        except Exception as e:
            self.fail(f"فشل في استيراد وحدات سطح المكتب: {e}")

def run_performance_tests():
    """اختبارات الأداء"""
    print("\n" + "="*50)
    print("اختبارات الأداء")
    print("="*50)
    
    import time
    
    # اختبار أداء قاعدة البيانات
    try:
        from models.transaction import TransactionManager
        
        start_time = time.time()
        transaction_manager = TransactionManager(use_mysql=False)
        transactions = transaction_manager.get_all_transactions()
        end_time = time.time()
        
        print(f"✓ تحميل {len(transactions)} معاملة في {end_time - start_time:.3f} ثانية")
        
    except Exception as e:
        print(f"✗ خطأ في اختبار الأداء: {e}")
    
    # اختبار أداء البحث
    try:
        start_time = time.time()
        results = transaction_manager.search_transactions({'subject': 'test'})
        end_time = time.time()
        
        print(f"✓ البحث في المعاملات في {end_time - start_time:.3f} ثانية")
        
    except Exception as e:
        print(f"✗ خطأ في اختبار البحث: {e}")

def run_integration_tests():
    """اختبارات التكامل"""
    print("\n" + "="*50)
    print("اختبارات التكامل")
    print("="*50)
    
    # اختبار التكامل بين النماذج
    try:
        from models.user import UserManager
        from models.transaction import TransactionManager
        from models.enums import EnumManager
        
        user_manager = UserManager(use_mysql=False)
        transaction_manager = TransactionManager(use_mysql=False)
        enum_manager = EnumManager(use_mysql=False)
        
        # اختبار الحصول على البيانات المترابطة
        users = user_manager.get_all_users()
        transactions = transaction_manager.get_all_transactions()
        statuses = enum_manager.get_request_statuses()
        
        print(f"✓ تم تحميل {len(users)} مستخدم، {len(transactions)} معاملة، {len(statuses)} حالة")
        
    except Exception as e:
        print(f"✗ خطأ في اختبار التكامل: {e}")

def main():
    """الدالة الرئيسية للاختبار"""
    print("="*60)
    print("اختبار شامل لنظام متابعة الوارد والصادر (IOTS)")
    print("="*60)
    
    # تشغيل اختبارات الوحدة
    print("\nتشغيل اختبارات الوحدة...")
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()
    
    # إضافة اختبارات قاعدة البيانات
    test_suite.addTest(unittest.makeSuite(TestDatabase))
    
    # إضافة اختبارات النماذج
    test_suite.addTest(unittest.makeSuite(TestUserModel))
    test_suite.addTest(unittest.makeSuite(TestTransactionModel))
    test_suite.addTest(unittest.makeSuite(TestEnumModel))
    
    # إضافة اختبارات النظم
    test_suite.addTest(unittest.makeSuite(TestSyncSystem))
    test_suite.addTest(unittest.makeSuite(TestReportsSystem))
    
    # إضافة اختبارات التطبيقات
    test_suite.addTest(unittest.makeSuite(TestWebApp))
    test_suite.addTest(unittest.makeSuite(TestDesktopApp))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # تشغيل اختبارات الأداء
    run_performance_tests()
    
    # تشغيل اختبارات التكامل
    run_integration_tests()
    
    # ملخص النتائج
    print("\n" + "="*60)
    print("ملخص نتائج الاختبار")
    print("="*60)
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    skipped = len(result.skipped) if hasattr(result, 'skipped') else 0
    passed = total_tests - failures - errors - skipped
    
    print(f"إجمالي الاختبارات: {total_tests}")
    print(f"نجح: {passed}")
    print(f"فشل: {failures}")
    print(f"أخطاء: {errors}")
    print(f"تم تخطيه: {skipped}")
    
    if failures == 0 and errors == 0:
        print("\n🎉 جميع الاختبارات نجحت!")
        return 0
    else:
        print(f"\n⚠ يوجد {failures + errors} اختبار فاشل")
        
        if result.failures:
            print("\nالاختبارات الفاشلة:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback.split('AssertionError: ')[-1].split('\\n')[0]}")
        
        if result.errors:
            print("\nأخطاء الاختبارات:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback.split('\\n')[-2]}")
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
