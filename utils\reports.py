# -*- coding: utf-8 -*-
"""
نظام التقارير
Reports System

يحتوي على دوال إنشاء وتصدير التقارير بصيغ مختلفة
"""

import os
from datetime import datetime, date
from pathlib import Path
import json

try:
    from reportlab.lib.pagesizes import A4, letter
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, cm
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

try:
    import openpyxl
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
    from openpyxl.utils import get_column_letter
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

from models.transaction import TransactionManager
from models.user import UserManager
from models.enums import EnumManager

class ReportGenerator:
    """مولد التقارير"""
    
    def __init__(self, use_mysql=True):
        self.transaction_manager = TransactionManager(use_mysql)
        self.user_manager = UserManager(use_mysql)
        self.enum_manager = EnumManager(use_mysql)
        
        # إعداد مجلد التقارير
        self.reports_dir = Path("reports")
        self.reports_dir.mkdir(exist_ok=True)
    
    def generate_transactions_report(self, filters=None, format="excel"):
        """إنشاء تقرير المعاملات"""
        try:
            # الحصول على البيانات
            if filters:
                transactions = self.transaction_manager.search_transactions(filters)
            else:
                transactions = self.transaction_manager.get_all_transactions()
            
            # إنشاء التقرير حسب التنسيق المطلوب
            if format.lower() == "excel" and OPENPYXL_AVAILABLE:
                return self._generate_excel_report(transactions)
            elif format.lower() == "pdf" and REPORTLAB_AVAILABLE:
                return self._generate_pdf_report(transactions)
            else:
                return self._generate_csv_report(transactions)
                
        except Exception as e:
            raise Exception(f"خطأ في إنشاء التقرير: {str(e)}")
    
    def _generate_excel_report(self, transactions):
        """إنشاء تقرير Excel"""
        # إنشاء ملف Excel جديد
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "تقرير المعاملات"
        
        # تنسيق الخطوط والألوان
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # عنوان التقرير
        ws.merge_cells('A1:H1')
        title_cell = ws['A1']
        title_cell.value = "تقرير المعاملات - نظام متابعة الوارد والصادر"
        title_cell.font = Font(bold=True, size=16)
        title_cell.alignment = Alignment(horizontal='center')
        
        # تاريخ التقرير
        ws.merge_cells('A2:H2')
        date_cell = ws['A2']
        date_cell.value = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        date_cell.alignment = Alignment(horizontal='center')
        
        # رؤوس الأعمدة
        headers = [
            "رقم الوارد", "تاريخ الوارد", "الموضوع", "الباحث الأول", 
            "نوع التأشيرة", "وارد من", "الحالة", "الأولوية"
        ]
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=4, column=col)
            cell.value = header
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center')
        
        # بيانات المعاملات
        for row, transaction in enumerate(transactions, 5):
            ws.cell(row=row, column=1, value=transaction.head_incoming_no or "")
            ws.cell(row=row, column=2, value=str(transaction.head_incoming_date) if transaction.head_incoming_date else "")
            ws.cell(row=row, column=3, value=transaction.subject or "")
            ws.cell(row=row, column=4, value=self._get_researcher_name(transaction.researcher_1_id))
            ws.cell(row=row, column=5, value=self._get_visa_type_name(transaction.visa_type_id))
            ws.cell(row=row, column=6, value=self._get_source_name(transaction.received_from_id))
            ws.cell(row=row, column=7, value=self._get_status_name(transaction.request_status_id))
            ws.cell(row=row, column=8, value=self._get_priority_text(transaction.priority))
            
            # تطبيق الحدود على جميع الخلايا
            for col in range(1, 9):
                ws.cell(row=row, column=col).border = border
        
        # تعديل عرض الأعمدة
        column_widths = [15, 12, 40, 20, 20, 25, 15, 12]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[get_column_letter(col)].width = width
        
        # إضافة ملخص إحصائي
        summary_row = len(transactions) + 6
        ws.cell(row=summary_row, column=1, value="إجمالي المعاملات:")
        ws.cell(row=summary_row, column=2, value=len(transactions))
        
        # حفظ الملف
        filename = f"تقرير_المعاملات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        filepath = self.reports_dir / filename
        wb.save(filepath)
        
        return str(filepath)
    
    def _generate_pdf_report(self, transactions):
        """إنشاء تقرير PDF"""
        filename = f"تقرير_المعاملات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        filepath = self.reports_dir / filename
        
        # إنشاء المستند
        doc = SimpleDocTemplate(str(filepath), pagesize=A4)
        story = []
        
        # الأنماط
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # وسط
        )
        
        # العنوان
        title = Paragraph("تقرير المعاملات - نظام متابعة الوارد والصادر", title_style)
        story.append(title)
        
        # تاريخ التقرير
        date_text = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        date_para = Paragraph(date_text, styles['Normal'])
        story.append(date_para)
        story.append(Spacer(1, 20))
        
        # إعداد بيانات الجدول
        data = [['رقم الوارد', 'التاريخ', 'الموضوع', 'الباحث', 'الحالة']]
        
        for transaction in transactions:
            row = [
                transaction.head_incoming_no or "",
                str(transaction.head_incoming_date) if transaction.head_incoming_date else "",
                (transaction.subject[:30] + "...") if transaction.subject and len(transaction.subject) > 30 else (transaction.subject or ""),
                self._get_researcher_name(transaction.researcher_1_id)[:15],
                self._get_status_name(transaction.request_status_id)
            ]
            data.append(row)
        
        # إنشاء الجدول
        table = Table(data, colWidths=[2*cm, 2*cm, 6*cm, 3*cm, 2*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        
        # ملخص إحصائي
        story.append(Spacer(1, 20))
        summary_text = f"إجمالي المعاملات: {len(transactions)}"
        summary_para = Paragraph(summary_text, styles['Normal'])
        story.append(summary_para)
        
        # بناء المستند
        doc.build(story)
        
        return str(filepath)
    
    def _generate_csv_report(self, transactions):
        """إنشاء تقرير CSV"""
        filename = f"تقرير_المعاملات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        filepath = self.reports_dir / filename
        
        with open(filepath, 'w', encoding='utf-8-sig', newline='') as f:
            # كتابة العنوان
            f.write("تقرير المعاملات - نظام متابعة الوارد والصادر\n")
            f.write(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n\n")
            
            # كتابة رؤوس الأعمدة
            headers = [
                "رقم الوارد", "تاريخ الوارد", "الموضوع", "الباحث الأول",
                "نوع التأشيرة", "وارد من", "الحالة", "الأولوية"
            ]
            f.write(",".join(headers) + "\n")
            
            # كتابة البيانات
            for transaction in transactions:
                row = [
                    f'"{transaction.head_incoming_no or ""}"',
                    f'"{str(transaction.head_incoming_date) if transaction.head_incoming_date else ""}"',
                    f'"{transaction.subject or ""}"',
                    f'"{self._get_researcher_name(transaction.researcher_1_id)}"',
                    f'"{self._get_visa_type_name(transaction.visa_type_id)}"',
                    f'"{self._get_source_name(transaction.received_from_id)}"',
                    f'"{self._get_status_name(transaction.request_status_id)}"',
                    f'"{self._get_priority_text(transaction.priority)}"'
                ]
                f.write(",".join(row) + "\n")
            
            # ملخص إحصائي
            f.write(f"\nإجمالي المعاملات: {len(transactions)}\n")
        
        return str(filepath)
    
    def generate_statistics_report(self, format="excel"):
        """إنشاء تقرير إحصائي"""
        try:
            # جمع الإحصائيات
            all_transactions = self.transaction_manager.get_all_transactions()
            
            stats = {
                'total_transactions': len(all_transactions),
                'by_status': {},
                'by_priority': {},
                'by_month': {},
                'by_researcher': {}
            }
            
            # إحصائيات حسب الحالة
            for transaction in all_transactions:
                status_name = self._get_status_name(transaction.request_status_id)
                stats['by_status'][status_name] = stats['by_status'].get(status_name, 0) + 1
                
                # إحصائيات حسب الأولوية
                priority_text = self._get_priority_text(transaction.priority)
                stats['by_priority'][priority_text] = stats['by_priority'].get(priority_text, 0) + 1
                
                # إحصائيات حسب الشهر
                if transaction.head_incoming_date:
                    month_key = transaction.head_incoming_date.strftime('%Y-%m')
                    stats['by_month'][month_key] = stats['by_month'].get(month_key, 0) + 1
                
                # إحصائيات حسب الباحث
                researcher_name = self._get_researcher_name(transaction.researcher_1_id)
                if researcher_name:
                    stats['by_researcher'][researcher_name] = stats['by_researcher'].get(researcher_name, 0) + 1
            
            if format.lower() == "excel" and OPENPYXL_AVAILABLE:
                return self._generate_excel_statistics(stats)
            else:
                return self._generate_json_statistics(stats)
                
        except Exception as e:
            raise Exception(f"خطأ في إنشاء التقرير الإحصائي: {str(e)}")
    
    def _generate_excel_statistics(self, stats):
        """إنشاء تقرير إحصائي Excel"""
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "الإحصائيات"
        
        # تنسيق
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        
        row = 1
        
        # العنوان
        ws.merge_cells(f'A{row}:B{row}')
        ws[f'A{row}'].value = "تقرير إحصائي - نظام متابعة الوارد والصادر"
        ws[f'A{row}'].font = Font(bold=True, size=16)
        row += 2
        
        # إجمالي المعاملات
        ws[f'A{row}'].value = "إجمالي المعاملات:"
        ws[f'B{row}'].value = stats['total_transactions']
        ws[f'A{row}'].font = header_font
        ws[f'A{row}'].fill = header_fill
        row += 2
        
        # إحصائيات حسب الحالة
        ws[f'A{row}'].value = "إحصائيات حسب الحالة"
        ws[f'A{row}'].font = header_font
        ws[f'A{row}'].fill = header_fill
        row += 1
        
        for status, count in stats['by_status'].items():
            ws[f'A{row}'].value = status
            ws[f'B{row}'].value = count
            row += 1
        
        row += 1
        
        # إحصائيات حسب الأولوية
        ws[f'A{row}'].value = "إحصائيات حسب الأولوية"
        ws[f'A{row}'].font = header_font
        ws[f'A{row}'].fill = header_fill
        row += 1
        
        for priority, count in stats['by_priority'].items():
            ws[f'A{row}'].value = priority
            ws[f'B{row}'].value = count
            row += 1
        
        # حفظ الملف
        filename = f"تقرير_إحصائي_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        filepath = self.reports_dir / filename
        wb.save(filepath)
        
        return str(filepath)
    
    def _generate_json_statistics(self, stats):
        """إنشاء تقرير إحصائي JSON"""
        filename = f"تقرير_إحصائي_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        filepath = self.reports_dir / filename
        
        report_data = {
            'title': 'تقرير إحصائي - نظام متابعة الوارد والصادر',
            'generated_at': datetime.now().isoformat(),
            'statistics': stats
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        return str(filepath)
    
    # دوال مساعدة
    def _get_researcher_name(self, researcher_id):
        """الحصول على اسم الباحث"""
        if not researcher_id:
            return "غير مسند"
        
        user = self.user_manager.get_user_by_id(researcher_id)
        return user.full_name if user else "غير معروف"
    
    def _get_visa_type_name(self, visa_type_id):
        """الحصول على نوع التأشيرة"""
        if not visa_type_id:
            return "غير محدد"
        
        visa_types = self.enum_manager.get_visa_types()
        for vt in visa_types:
            if vt.id == visa_type_id:
                return vt.visa_type
        return "غير معروف"
    
    def _get_source_name(self, source_id):
        """الحصول على مصدر الورود"""
        if not source_id:
            return "غير محدد"
        
        sources = self.enum_manager.get_received_from_sources()
        for source in sources:
            if source.id == source_id:
                return source.received_from
        return "غير معروف"
    
    def _get_status_name(self, status_id):
        """الحصول على اسم الحالة"""
        if not status_id:
            return "غير محدد"
        
        statuses = self.enum_manager.get_request_statuses()
        for status in statuses:
            if status.id == status_id:
                return status.request_status
        return "غير معروف"
    
    def _get_priority_text(self, priority):
        """الحصول على نص الأولوية"""
        priority_map = {
            'low': 'منخفضة',
            'medium': 'متوسطة', 
            'high': 'مرتفعة',
            'urgent': 'عاجلة'
        }
        return priority_map.get(priority, 'متوسطة')

def test_reports():
    """اختبار نظام التقارير"""
    print("اختبار نظام التقارير...")
    
    try:
        generator = ReportGenerator(use_mysql=False)
        
        # اختبار تقرير المعاملات
        print("إنشاء تقرير المعاملات...")
        excel_file = generator.generate_transactions_report(format="excel")
        print(f"✓ تم إنشاء تقرير Excel: {excel_file}")
        
        csv_file = generator.generate_transactions_report(format="csv")
        print(f"✓ تم إنشاء تقرير CSV: {csv_file}")
        
        # اختبار التقرير الإحصائي
        print("إنشاء التقرير الإحصائي...")
        stats_file = generator.generate_statistics_report()
        print(f"✓ تم إنشاء التقرير الإحصائي: {stats_file}")
        
        print("✅ تم اختبار نظام التقارير بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التقارير: {e}")

if __name__ == "__main__":
    test_reports()
