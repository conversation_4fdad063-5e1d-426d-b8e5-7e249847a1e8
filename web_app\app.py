# -*- coding: utf-8 -*-
"""
تطبيق الويب الرئيسي
Main Web Application

يحتوي على إعدادات Flask والمسارات الأساسية
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import LoginManager, login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash
import os
from datetime import datetime, timedelta
import json

# استيراد النماذج
from models.user import UserManager, load_user
from models.transaction import TransactionManager
from models.enums import EnumManager
from config.database import DatabaseConnection

def create_app():
    """إنشاء تطبيق Flask"""
    app = Flask(__name__)
    
    # إعدادات التطبيق
    app.config['SECRET_KEY'] = 'your-secret-key-change-in-production'
    app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=1)
    
    # إعداد Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user_callback(user_id):
        return load_user(user_id)
    
    # المسارات الأساسية
    @app.route('/')
    def index():
        """الصفحة الرئيسية"""
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('login'))
    
    @app.route('/login', methods=['GET', 'POST'])
    def login():
        """صفحة تسجيل الدخول"""
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        
        if request.method == 'POST':
            username = request.form.get('username')
            password = request.form.get('password')
            remember = bool(request.form.get('remember'))
            
            if not username or not password:
                flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
                return render_template('auth/login.html')
            
            user_manager = UserManager()
            user, message = user_manager.authenticate_user(username, password)
            
            if user:
                login_user(user, remember=remember)
                flash(f'مرحباً {user.full_name}', 'success')
                
                # إعادة التوجيه للصفحة المطلوبة أو لوحة التحكم
                next_page = request.args.get('next')
                return redirect(next_page) if next_page else redirect(url_for('dashboard'))
            else:
                flash(message, 'error')
        
        return render_template('auth/login.html')
    
    @app.route('/logout')
    @login_required
    def logout():
        """تسجيل الخروج"""
        logout_user()
        flash('تم تسجيل الخروج بنجاح', 'info')
        return redirect(url_for('login'))
    
    @app.route('/dashboard')
    @login_required
    def dashboard():
        """لوحة التحكم الرئيسية"""
        try:
            transaction_manager = TransactionManager()
            
            # إحصائيات عامة
            all_transactions = transaction_manager.get_all_transactions()
            total_transactions = len(all_transactions)
            
            # إحصائيات حسب الحالة (تحتاج تحسين لاحقاً)
            new_transactions = len([t for t in all_transactions if t.request_status_id == 1])
            in_progress = len([t for t in all_transactions if t.request_status_id == 2])
            completed = len([t for t in all_transactions if t.request_status_id == 4])
            
            # المعاملات المسندة للمستخدم الحالي
            user_transactions = transaction_manager.get_transactions_by_researcher(current_user.user_id)
            
            # أحدث المعاملات (آخر 5)
            recent_transactions = all_transactions[:5]
            
            stats = {
                'total_transactions': total_transactions,
                'new_transactions': new_transactions,
                'in_progress': in_progress,
                'completed': completed,
                'user_transactions': len(user_transactions)
            }
            
            return render_template('dashboard/index.html', 
                                 stats=stats,
                                 recent_transactions=recent_transactions,
                                 user_transactions=user_transactions[:5])
        
        except Exception as e:
            flash(f'خطأ في تحميل لوحة التحكم: {str(e)}', 'error')
            return render_template('dashboard/index.html', 
                                 stats={}, 
                                 recent_transactions=[], 
                                 user_transactions=[])
    
    @app.route('/transactions')
    @login_required
    def transactions():
        """صفحة المعاملات"""
        try:
            transaction_manager = TransactionManager()
            enum_manager = EnumManager()
            
            # الحصول على المعاملات
            if current_user.is_admin():
                all_transactions = transaction_manager.get_all_transactions()
            else:
                all_transactions = transaction_manager.get_transactions_by_researcher(current_user.user_id)
            
            # الحصول على البيانات المساعدة للفلاتر
            visa_types = enum_manager.get_visa_types()
            sources = enum_manager.get_received_from_sources()
            actions = enum_manager.get_actions_taken()
            statuses = enum_manager.get_request_statuses()
            
            return render_template('transactions/list.html',
                                 transactions=all_transactions,
                                 visa_types=visa_types,
                                 sources=sources,
                                 actions=actions,
                                 statuses=statuses)
        
        except Exception as e:
            flash(f'خطأ في تحميل المعاملات: {str(e)}', 'error')
            return render_template('transactions/list.html',
                                 transactions=[],
                                 visa_types=[],
                                 sources=[],
                                 actions=[],
                                 statuses=[])
    
    @app.route('/transactions/new', methods=['GET', 'POST'])
    @login_required
    def new_transaction():
        """إنشاء معاملة جديدة"""
        enum_manager = EnumManager()
        user_manager = UserManager()
        
        # الحصول على البيانات المساعدة
        visa_types = enum_manager.get_visa_types()
        sources = enum_manager.get_received_from_sources()
        actions = enum_manager.get_actions_taken()
        statuses = enum_manager.get_request_statuses()
        users = user_manager.get_all_users()
        
        if request.method == 'POST':
            try:
                transaction_data = {
                    'head_incoming_no': request.form.get('head_incoming_no'),
                    'head_incoming_date': request.form.get('head_incoming_date'),
                    'subject': request.form.get('subject'),
                    'researcher_notes': request.form.get('researcher_notes'),
                    'priority': request.form.get('priority', 'medium'),
                    'due_date': request.form.get('due_date') or None,
                    'user_id': current_user.user_id,
                    'researcher_1_id': request.form.get('researcher_1_id') or None,
                    'researcher_2_id': request.form.get('researcher_2_id') or None,
                    'visa_type_id': request.form.get('visa_type_id') or None,
                    'received_from_id': request.form.get('received_from_id') or None,
                    'action_taken_id': request.form.get('action_taken_id') or None,
                    'request_status_id': request.form.get('request_status_id') or 1
                }
                
                # التحقق من البيانات المطلوبة
                if not all([transaction_data['head_incoming_no'], 
                           transaction_data['head_incoming_date'], 
                           transaction_data['subject']]):
                    flash('يرجى ملء جميع الحقول المطلوبة', 'error')
                    return render_template('transactions/form.html',
                                         visa_types=visa_types,
                                         sources=sources,
                                         actions=actions,
                                         statuses=statuses,
                                         users=users,
                                         transaction=None)
                
                transaction_manager = TransactionManager()
                success, message = transaction_manager.create_transaction(transaction_data)
                
                if success:
                    flash(message, 'success')
                    return redirect(url_for('transactions'))
                else:
                    flash(message, 'error')
            
            except Exception as e:
                flash(f'خطأ في إنشاء المعاملة: {str(e)}', 'error')
        
        return render_template('transactions/form.html',
                             visa_types=visa_types,
                             sources=sources,
                             actions=actions,
                             statuses=statuses,
                             users=users,
                             transaction=None)
    
    @app.route('/api/transactions/search')
    @login_required
    def search_transactions():
        """البحث في المعاملات عبر API"""
        try:
            search_params = {
                'head_incoming_no': request.args.get('head_incoming_no'),
                'subject': request.args.get('subject'),
                'date_from': request.args.get('date_from'),
                'date_to': request.args.get('date_to'),
                'researcher_id': request.args.get('researcher_id'),
                'status_id': request.args.get('status_id')
            }

            # إزالة القيم الفارغة
            search_params = {k: v for k, v in search_params.items() if v}

            transaction_manager = TransactionManager()
            results = transaction_manager.search_transactions(search_params)

            # تحويل النتائج إلى JSON
            transactions_data = [t.to_dict() for t in results]

            return jsonify({
                'success': True,
                'transactions': transactions_data,
                'count': len(transactions_data)
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/reports')
    @login_required
    def reports():
        """صفحة التقارير"""
        return render_template('reports/index.html')

    @app.route('/reports/generate', methods=['POST'])
    @login_required
    def generate_report():
        """إنشاء تقرير"""
        try:
            from utils.reports import ReportGenerator

            report_type = request.form.get('report_type', 'transactions')
            format_type = request.form.get('format', 'excel')

            # فلاتر التقرير
            filters = {}
            if request.form.get('date_from'):
                filters['date_from'] = request.form.get('date_from')
            if request.form.get('date_to'):
                filters['date_to'] = request.form.get('date_to')
            if request.form.get('status_id'):
                filters['status_id'] = request.form.get('status_id')
            if request.form.get('researcher_id'):
                filters['researcher_id'] = request.form.get('researcher_id')

            generator = ReportGenerator()

            if report_type == 'statistics':
                file_path = generator.generate_statistics_report(format=format_type)
            else:
                file_path = generator.generate_transactions_report(filters=filters if filters else None, format=format_type)

            # إرسال الملف للتحميل
            from flask import send_file
            return send_file(file_path, as_attachment=True)

        except Exception as e:
            flash(f'خطأ في إنشاء التقرير: {str(e)}', 'error')
            return redirect(url_for('reports'))

    @app.route('/api/reports/preview')
    @login_required
    def preview_report():
        """معاينة التقرير"""
        try:
            from utils.reports import ReportGenerator

            report_type = request.args.get('type', 'transactions')

            generator = ReportGenerator()

            if report_type == 'statistics':
                # إحصائيات سريعة
                all_transactions = generator.transaction_manager.get_all_transactions()

                stats = {
                    'total': len(all_transactions),
                    'by_status': {},
                    'by_priority': {}
                }

                for transaction in all_transactions:
                    # حسب الحالة
                    status_name = generator._get_status_name(transaction.request_status_id)
                    stats['by_status'][status_name] = stats['by_status'].get(status_name, 0) + 1

                    # حسب الأولوية
                    priority_text = generator._get_priority_text(transaction.priority)
                    stats['by_priority'][priority_text] = stats['by_priority'].get(priority_text, 0) + 1

                return jsonify({
                    'success': True,
                    'data': stats
                })
            else:
                # معاينة المعاملات (أول 10)
                transactions = generator.transaction_manager.get_all_transactions()[:10]
                transactions_data = []

                for t in transactions:
                    transactions_data.append({
                        'head_incoming_no': t.head_incoming_no,
                        'head_incoming_date': str(t.head_incoming_date) if t.head_incoming_date else '',
                        'subject': t.subject,
                        'researcher': generator._get_researcher_name(t.researcher_1_id),
                        'status': generator._get_status_name(t.request_status_id),
                        'priority': generator._get_priority_text(t.priority)
                    })

                return jsonify({
                    'success': True,
                    'data': transactions_data,
                    'total': len(generator.transaction_manager.get_all_transactions())
                })

        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    # معالج الأخطاء
    @app.errorhandler(404)
    def not_found(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return render_template('errors/500.html'), 500
    
    # فلاتر Jinja2 مخصصة
    @app.template_filter('datetime')
    def datetime_filter(value):
        """فلتر تنسيق التاريخ والوقت"""
        if value is None:
            return ''
        if isinstance(value, str):
            try:
                value = datetime.fromisoformat(value.replace('Z', '+00:00'))
            except:
                return value
        return value.strftime('%Y-%m-%d %H:%M')

    @app.template_filter('date')
    def date_filter(value):
        """فلتر تنسيق التاريخ"""
        if value is None:
            return ''
        if isinstance(value, str):
            try:
                value = datetime.fromisoformat(value.replace('Z', '+00:00'))
            except:
                return value
        return value.strftime('%Y-%m-%d')

    # إضافة دالة moment للقوالب
    @app.template_global()
    def moment():
        """دالة moment بسيطة لإرجاع التاريخ الحالي"""
        class MomentHelper:
            def __init__(self):
                self.now = datetime.now()

            @property
            def year(self):
                return self.now.year

            def format(self, format_str):
                # تحويل بسيط لبعض التنسيقات الشائعة
                if 'dddd' in format_str:
                    return self.now.strftime('%A، %d %B %Y')
                return self.now.strftime('%Y-%m-%d')

        return MomentHelper()
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
