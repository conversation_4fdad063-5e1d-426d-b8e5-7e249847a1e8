/* خط Cairo للغة العربية */
/* Cairo Font for Arabic Language */

@font-face {
    font-family: 'Cairo';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: local('Cairo Light'), local('Cairo-Light'),
         url('../fonts/cairo-light.woff2') format('woff2'),
         url('../fonts/cairo-light.woff') format('woff');
}

@font-face {
    font-family: 'Cairo';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: local('Cairo Regular'), local('Cairo-Regular'),
         url('../fonts/cairo-regular.woff2') format('woff2'),
         url('../fonts/cairo-regular.woff') format('woff');
}

@font-face {
    font-family: 'Cairo';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: local('Cairo SemiBold'), local('Cairo-SemiBold'),
         url('../fonts/cairo-semibold.woff2') format('woff2'),
         url('../fonts/cairo-semibold.woff') format('woff');
}

@font-face {
    font-family: 'Cairo';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: local('Cairo Bold'), local('Cairo-Bold'),
         url('../fonts/cairo-bold.woff2') format('woff2'),
         url('../fonts/cairo-bold.woff') format('woff');
}

/* تطبيق الخط على العناصر العربية */
body,
.arabic-text,
h1, h2, h3, h4, h5, h6,
.navbar-brand,
.nav-link,
.btn,
.form-label,
.form-control,
.form-select,
.card-title,
.card-text,
.table,
.alert,
.badge {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* تحسين عرض النص العربي */
.arabic-text {
    direction: rtl;
    text-align: right;
    line-height: 1.8;
}

/* تحسين المسافات للنصوص العربية */
p, .form-text, .text-muted {
    line-height: 1.7;
}

/* تحسين عرض الأرقام العربية */
.arabic-numbers {
    font-feature-settings: "lnum" 1;
}

/* تحسين عرض التواريخ */
.date-text {
    direction: ltr;
    text-align: left;
    font-variant-numeric: tabular-nums;
}
