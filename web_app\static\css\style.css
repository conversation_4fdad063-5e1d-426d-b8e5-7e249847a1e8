/* نظام متابعة الوارد والصادر - الأنماط المخصصة */
/* IOTS - Custom Styles */

/* الخطوط */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

/* المتغيرات */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

/* الإعدادات العامة */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #f5f6fa;
}

/* تحسين الخطوط العربية */
.arabic-text {
    font-family: 'Cairo', sans-serif;
    font-weight: 400;
}

/* الأزرار المخصصة */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 8px 16px;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* البطاقات */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

/* شريط التنقل */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 0.75rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    margin: 0 2px;
    transition: var(--transition);
}

.nav-link:hover {
    background-color: rgba(255,255,255,0.1);
}

.dropdown-menu {
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: var(--border-radius);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--light-color);
}

/* النماذج */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    padding: 0.75rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    padding: 0.75rem;
}

/* الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem 0.75rem;
}

.table tbody td {
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* الشارات */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
    border-radius: 6px;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.25rem;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* الأيقونات */
.fas, .far {
    margin-left: 0.25rem;
}

/* الحاويات */
.container-fluid {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

/* الفوتر */
footer {
    margin-top: auto;
    background-color: white !important;
    border-top: 1px solid #e9ecef;
}

/* الرسوم المتحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn {
        padding: 6px 12px;
        font-size: 0.875rem;
    }
}

/* تحسينات إضافية للواجهة العربية */
.text-right {
    text-align: right !important;
}

.text-left {
    text-align: left !important;
}

/* تخصيص الألوان حسب الحالة */
.status-new {
    background-color: #17a2b8;
    color: white;
}

.status-in-progress {
    background-color: #ffc107;
    color: #212529;
}

.status-completed {
    background-color: #28a745;
    color: white;
}

.status-rejected {
    background-color: #dc3545;
    color: white;
}

.status-pending {
    background-color: #6c757d;
    color: white;
}

/* تحسين مظهر القوائم المنسدلة */
.list-group-item {
    border: none;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem;
    transition: var(--transition);
}

.list-group-item:last-child {
    border-bottom: none;
}

.list-group-item:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* تحسين مظهر النماذج */
.input-group .btn {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.input-group .form-control {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

/* تحسين مظهر الصفحات الفارغة */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* تحسين مظهر البحث */
.search-box {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
}

/* تحسين مظهر الإحصائيات */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}
