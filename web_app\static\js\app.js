/**
 * نظام متابعة الوارد والصادر - JavaScript الرئيسي
 * IOTS - Main JavaScript File
 */

// إعدادات عامة
const IOTS = {
    // إعدادات API
    api: {
        baseUrl: '/api',
        timeout: 30000
    },
    
    // إعدادات الواجهة
    ui: {
        animationDuration: 300,
        toastDuration: 5000
    },
    
    // حالة التطبيق
    state: {
        isLoading: false,
        currentPage: 1,
        itemsPerPage: 10
    }
};

// دوال مساعدة عامة
const Utils = {
    /**
     * عرض رسالة تنبيه
     */
    showAlert: function(message, type = 'info', duration = 5000) {
        const alertContainer = document.getElementById('alert-container') || this.createAlertContainer();
        
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            <i class="fas fa-${this.getAlertIcon(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        alertContainer.appendChild(alertDiv);
        
        // إزالة التنبيه تلقائياً
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, duration);
    },
    
    /**
     * إنشاء حاوي التنبيهات
     */
    createAlertContainer: function() {
        const container = document.createElement('div');
        container.id = 'alert-container';
        container.className = 'position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
        return container;
    },
    
    /**
     * الحصول على أيقونة التنبيه
     */
    getAlertIcon: function(type) {
        const icons = {
            'success': 'check-circle',
            'danger': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    },
    
    /**
     * تنسيق التاريخ
     */
    formatDate: function(dateString, format = 'YYYY-MM-DD') {
        if (!dateString) return '';
        
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        
        switch (format) {
            case 'YYYY-MM-DD':
                return `${year}-${month}-${day}`;
            case 'DD/MM/YYYY':
                return `${day}/${month}/${year}`;
            case 'DD-MM-YYYY':
                return `${day}-${month}-${year}`;
            default:
                return `${year}-${month}-${day}`;
        }
    },
    
    /**
     * تنسيق التاريخ والوقت
     */
    formatDateTime: function(dateString) {
        if (!dateString) return '';
        
        const date = new Date(dateString);
        return date.toLocaleString('ar-SA', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    },
    
    /**
     * التحقق من صحة البريد الإلكتروني
     */
    validateEmail: function(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },
    
    /**
     * التحقق من صحة رقم الهاتف
     */
    validatePhone: function(phone) {
        const re = /^[\+]?[0-9\s\-\(\)]{10,}$/;
        return re.test(phone);
    },
    
    /**
     * تنظيف النص
     */
    sanitizeText: function(text) {
        if (!text) return '';
        return text.trim().replace(/\s+/g, ' ');
    },
    
    /**
     * إظهار مؤشر التحميل
     */
    showLoading: function(element, text = 'جاري التحميل...') {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element) {
            element.disabled = true;
            element.innerHTML = `
                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                ${text}
            `;
        }
        
        IOTS.state.isLoading = true;
    },
    
    /**
     * إخفاء مؤشر التحميل
     */
    hideLoading: function(element, originalText) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element) {
            element.disabled = false;
            element.innerHTML = originalText;
        }
        
        IOTS.state.isLoading = false;
    },
    
    /**
     * تأكيد الحذف
     */
    confirmDelete: function(message = 'هل أنت متأكد من الحذف؟') {
        return confirm(message);
    }
};

// دوال API
const API = {
    /**
     * طلب GET
     */
    get: async function(url, params = {}) {
        try {
            const queryString = new URLSearchParams(params).toString();
            const fullUrl = `${IOTS.api.baseUrl}${url}${queryString ? '?' + queryString : ''}`;
            
            const response = await fetch(fullUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            
            return await response.json();
        } catch (error) {
            console.error('API GET Error:', error);
            throw error;
        }
    },
    
    /**
     * طلب POST
     */
    post: async function(url, data = {}) {
        try {
            const response = await fetch(`${IOTS.api.baseUrl}${url}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            return await response.json();
        } catch (error) {
            console.error('API POST Error:', error);
            throw error;
        }
    },
    
    /**
     * البحث في المعاملات
     */
    searchTransactions: async function(searchParams) {
        return await this.get('/transactions/search', searchParams);
    }
};

// دوال الجداول
const DataTable = {
    /**
     * تهيئة جدول البيانات
     */
    init: function(tableId, options = {}) {
        const table = document.getElementById(tableId);
        if (!table) return;
        
        // إضافة وظائف الفرز
        this.addSortingFeature(table);
        
        // إضافة وظائف البحث
        if (options.searchable) {
            this.addSearchFeature(table, options.searchInputId);
        }
        
        // إضافة وظائف الترقيم
        if (options.paginated) {
            this.addPaginationFeature(table, options.itemsPerPage || 10);
        }
    },
    
    /**
     * إضافة وظيفة الفرز
     */
    addSortingFeature: function(table) {
        const headers = table.querySelectorAll('th[data-sortable]');
        
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.innerHTML += ' <i class="fas fa-sort text-muted"></i>';
            
            header.addEventListener('click', () => {
                this.sortTable(table, header);
            });
        });
    },
    
    /**
     * فرز الجدول
     */
    sortTable: function(table, header) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const isAscending = !header.classList.contains('sort-asc');
        
        // إزالة فئات الفرز من جميع الرؤوس
        table.querySelectorAll('th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
            const icon = th.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sort text-muted';
            }
        });
        
        // إضافة فئة الفرز للرأس الحالي
        header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
        const icon = header.querySelector('i');
        if (icon) {
            icon.className = `fas fa-sort-${isAscending ? 'up' : 'down'} text-primary`;
        }
        
        // فرز الصفوف
        rows.sort((a, b) => {
            const aValue = a.children[columnIndex].textContent.trim();
            const bValue = b.children[columnIndex].textContent.trim();
            
            if (isAscending) {
                return aValue.localeCompare(bValue, 'ar');
            } else {
                return bValue.localeCompare(aValue, 'ar');
            }
        });
        
        // إعادة ترتيب الصفوف
        rows.forEach(row => tbody.appendChild(row));
    },
    
    /**
     * إضافة وظيفة البحث
     */
    addSearchFeature: function(table, searchInputId) {
        const searchInput = document.getElementById(searchInputId);
        if (!searchInput) return;
        
        searchInput.addEventListener('input', (e) => {
            this.filterTable(table, e.target.value);
        });
    },
    
    /**
     * تصفية الجدول
     */
    filterTable: function(table, searchTerm) {
        const tbody = table.querySelector('tbody');
        const rows = tbody.querySelectorAll('tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const shouldShow = text.includes(searchTerm.toLowerCase());
            row.style.display = shouldShow ? '' : 'none';
        });
    }
};

// دوال النماذج
const Forms = {
    /**
     * التحقق من صحة النموذج
     */
    validate: function(formId) {
        const form = document.getElementById(formId);
        if (!form) return false;
        
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, 'هذا الحقل مطلوب');
                isValid = false;
            } else {
                this.clearFieldError(field);
            }
        });
        
        return isValid;
    },
    
    /**
     * إظهار خطأ في الحقل
     */
    showFieldError: function(field, message) {
        field.classList.add('is-invalid');
        
        let errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            field.parentNode.appendChild(errorDiv);
        }
        
        errorDiv.textContent = message;
    },
    
    /**
     * إزالة خطأ الحقل
     */
    clearFieldError: function(field) {
        field.classList.remove('is-invalid');
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    },
    
    /**
     * إعادة تعيين النموذج
     */
    reset: function(formId) {
        const form = document.getElementById(formId);
        if (form) {
            form.reset();
            
            // إزالة جميع رسائل الخطأ
            form.querySelectorAll('.is-invalid').forEach(field => {
                this.clearFieldError(field);
            });
        }
    }
};

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تهيئة Bootstrap popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // تهيئة الجداول
    const tables = document.querySelectorAll('.data-table');
    tables.forEach(table => {
        DataTable.init(table.id, {
            searchable: table.dataset.searchable === 'true',
            paginated: table.dataset.paginated === 'true',
            searchInputId: table.dataset.searchInput,
            itemsPerPage: parseInt(table.dataset.itemsPerPage) || 10
        });
    });
    
    // إضافة مستمعي الأحداث للنماذج
    const forms = document.querySelectorAll('form[data-validate]');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!Forms.validate(this.id)) {
                e.preventDefault();
                Utils.showAlert('يرجى تصحيح الأخطاء في النموذج', 'danger');
            }
        });
    });
    
    // تحسين تجربة المستخدم
    const buttons = document.querySelectorAll('.btn[type="submit"]');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            const form = this.closest('form');
            if (form && Forms.validate(form.id)) {
                const originalText = this.innerHTML;
                Utils.showLoading(this, 'جاري المعالجة...');
                
                // إعادة النص الأصلي بعد 5 ثوان (في حالة عدم إعادة تحميل الصفحة)
                setTimeout(() => {
                    Utils.hideLoading(this, originalText);
                }, 5000);
            }
        });
    });
    
    console.log('IOTS Application initialized successfully');
});

// تصدير الكائنات للاستخدام العام
window.IOTS = IOTS;
window.Utils = Utils;
window.API = API;
window.DataTable = DataTable;
window.Forms = Forms;
