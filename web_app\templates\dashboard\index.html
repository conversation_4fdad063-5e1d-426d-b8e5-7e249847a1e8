{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام متابعة الوارد والصادر{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- ترحيب -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">مرحباً، {{ current_user.full_name }}</h2>
                    <p class="text-muted mb-0">
                        <i class="fas fa-calendar me-1"></i>
                        {{ moment().format('dddd، DD MMMM YYYY') }}
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('new_transaction') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        معاملة جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient rounded-circle p-3">
                                <i class="fas fa-file-alt text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">إجمالي المعاملات</h6>
                            <h3 class="mb-0">{{ stats.total_transactions or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient rounded-circle p-3">
                                <i class="fas fa-clock text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">معاملات جديدة</h6>
                            <h3 class="mb-0">{{ stats.new_transactions or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient rounded-circle p-3">
                                <i class="fas fa-spinner text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">قيد المعالجة</h6>
                            <h3 class="mb-0">{{ stats.in_progress or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient rounded-circle p-3">
                                <i class="fas fa-check text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">مكتملة</h6>
                            <h3 class="mb-0">{{ stats.completed or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- أحدث المعاملات -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2 text-primary"></i>
                            أحدث المعاملات
                        </h5>
                        <a href="{{ url_for('transactions') }}" class="btn btn-sm btn-outline-primary">
                            عرض الكل
                            <i class="fas fa-arrow-left ms-1"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if recent_transactions %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الوارد</th>
                                    <th>الموضوع</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                <tr>
                                    <td>
                                        <strong>{{ transaction.head_incoming_no }}</strong>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 300px;">
                                            {{ transaction.subject }}
                                        </div>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ transaction.head_incoming_date|date }}
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">جديد</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد معاملات حديثة</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- المعاملات المسندة لي -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">
                        <i class="fas fa-user-check me-2 text-success"></i>
                        المعاملات المسندة لي
                    </h5>
                </div>
                <div class="card-body">
                    {% if user_transactions %}
                    <div class="list-group list-group-flush">
                        {% for transaction in user_transactions %}
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ transaction.head_incoming_no }}</h6>
                                    <p class="mb-1 text-truncate" style="max-width: 200px;">
                                        {{ transaction.subject }}
                                    </p>
                                    <small class="text-muted">
                                        {{ transaction.head_incoming_date|date }}
                                    </small>
                                </div>
                                <span class="badge bg-warning">قيد المعالجة</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-tasks fa-2x text-muted mb-3"></i>
                        <p class="text-muted mb-0">لا توجد معاملات مسندة</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- الرسم البياني -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2 text-info"></i>
                        إحصائيات المعاملات الشهرية
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="transactionsChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        transition: transform 0.2s ease-in-out;
    }
    
    .card:hover {
        transform: translateY(-2px);
    }
    
    .bg-gradient {
        background: linear-gradient(45deg, var(--bs-primary), var(--bs-primary-dark)) !important;
    }
    
    .table-hover tbody tr:hover {
        background-color: rgba(0,123,255,0.05);
    }
    
    .list-group-item {
        transition: background-color 0.2s ease;
    }
    
    .list-group-item:hover {
        background-color: rgba(0,123,255,0.05);
    }
</style>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="{{ url_for('static', filename='js/chart.min.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // إعداد الرسم البياني
    const ctx = document.getElementById('transactionsChart').getContext('2d');
    
    // بيانات تجريبية - يجب استبدالها ببيانات حقيقية من الخادم
    const chartData = {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
        datasets: [{
            label: 'المعاملات الواردة',
            data: [12, 19, 3, 5, 2, 3],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'المعاملات المكتملة',
            data: [8, 15, 2, 4, 1, 2],
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.1)',
            tension: 0.4,
            fill: true
        }]
    };
    
    const config = {
        type: 'line',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                }
            },
            elements: {
                point: {
                    radius: 4,
                    hoverRadius: 6
                }
            }
        }
    };
    
    new Chart(ctx, config);
    
    // تحديث التاريخ
    const dateElement = document.querySelector('.text-muted');
    if (dateElement && dateElement.textContent.includes('moment()')) {
        const now = new Date();
        const options = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        const arabicDate = now.toLocaleDateString('ar-SA', options);
        dateElement.innerHTML = '<i class="fas fa-calendar me-1"></i>' + arabicDate;
    }
});
</script>
{% endblock %}
