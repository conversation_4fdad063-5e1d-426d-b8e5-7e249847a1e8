{% extends "base.html" %}

{% block title %}الصفحة غير موجودة - نظام متابعة الوارد والصادر{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center">
            <div class="error-page">
                <div class="error-code">
                    <h1 class="display-1 text-primary">404</h1>
                </div>
                
                <div class="error-message">
                    <h2 class="mb-3">الصفحة غير موجودة</h2>
                    <p class="text-muted mb-4">
                        عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.
                    </p>
                </div>
                
                <div class="error-actions">
                    <a href="{{ url_for('dashboard') }}" class="btn btn-primary me-2">
                        <i class="fas fa-home me-2"></i>
                        العودة للرئيسية
                    </a>
                    <a href="{{ url_for('transactions') }}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>
                        المعاملات
                    </a>
                </div>
                
                <div class="error-illustration mt-5">
                    <i class="fas fa-search fa-5x text-muted opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .error-page {
        padding: 3rem 0;
    }
    
    .error-code h1 {
        font-weight: 700;
        font-size: 8rem;
        line-height: 1;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    
    .error-message h2 {
        color: #495057;
        font-weight: 600;
    }
    
    .error-actions .btn {
        border-radius: 25px;
        padding: 10px 25px;
        font-weight: 600;
    }
    
    .error-illustration {
        animation: float 3s ease-in-out infinite;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }
    
    @media (max-width: 768px) {
        .error-code h1 {
            font-size: 5rem;
        }
        
        .error-actions .btn {
            display: block;
            width: 100%;
            margin-bottom: 10px;
        }
    }
</style>
{% endblock %}
