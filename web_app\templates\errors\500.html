{% extends "base.html" %}

{% block title %}خطأ في الخادم - نظام متابعة الوارد والصادر{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center">
            <div class="error-page">
                <div class="error-code">
                    <h1 class="display-1 text-danger">500</h1>
                </div>
                
                <div class="error-message">
                    <h2 class="mb-3">خطأ في الخادم</h2>
                    <p class="text-muted mb-4">
                        عذراً، حدث خطأ غير متوقع في الخادم. يرجى المحاولة مرة أخرى لاحقاً.
                    </p>
                </div>
                
                <div class="error-actions">
                    <a href="{{ url_for('dashboard') }}" class="btn btn-primary me-2">
                        <i class="fas fa-home me-2"></i>
                        العودة للرئيسية
                    </a>
                    <button onclick="window.location.reload()" class="btn btn-outline-primary">
                        <i class="fas fa-redo me-2"></i>
                        إعادة المحاولة
                    </button>
                </div>
                
                <div class="error-illustration mt-5">
                    <i class="fas fa-exclamation-triangle fa-5x text-warning opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .error-page {
        padding: 3rem 0;
    }
    
    .error-code h1 {
        font-weight: 700;
        font-size: 8rem;
        line-height: 1;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    
    .error-message h2 {
        color: #495057;
        font-weight: 600;
    }
    
    .error-actions .btn {
        border-radius: 25px;
        padding: 10px 25px;
        font-weight: 600;
    }
    
    .error-illustration {
        animation: pulse 2s ease-in-out infinite;
    }
    
    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }
    
    @media (max-width: 768px) {
        .error-code h1 {
            font-size: 5rem;
        }
        
        .error-actions .btn {
            display: block;
            width: 100%;
            margin-bottom: 10px;
        }
    }
</style>
{% endblock %}
