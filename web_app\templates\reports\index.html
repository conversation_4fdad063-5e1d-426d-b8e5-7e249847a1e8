{% extends "base.html" %}

{% block title %}التقارير - نظام متابعة الوارد والصادر{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-chart-bar me-2 text-primary"></i>
                        التقارير والإحصائيات
                    </h2>
                    <p class="text-muted mb-0">إنشاء وتصدير التقارير المختلفة</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- قائمة أنواع التقارير -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2 text-success"></i>
                        أنواع التقارير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <button type="button" class="list-group-item list-group-item-action report-type-btn active" 
                                data-type="transactions">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file-alt me-3 text-primary"></i>
                                <div>
                                    <h6 class="mb-1">تقرير المعاملات</h6>
                                    <small class="text-muted">قائمة شاملة بجميع المعاملات</small>
                                </div>
                            </div>
                        </button>
                        
                        <button type="button" class="list-group-item list-group-item-action report-type-btn" 
                                data-type="statistics">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-chart-pie me-3 text-info"></i>
                                <div>
                                    <h6 class="mb-1">التقرير الإحصائي</h6>
                                    <small class="text-muted">إحصائيات وتحليلات شاملة</small>
                                </div>
                            </div>
                        </button>
                        
                        <button type="button" class="list-group-item list-group-item-action report-type-btn" 
                                data-type="monthly">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-calendar me-3 text-warning"></i>
                                <div>
                                    <h6 class="mb-1">التقرير الشهري</h6>
                                    <small class="text-muted">ملخص المعاملات الشهرية</small>
                                </div>
                            </div>
                        </button>
                        
                        <button type="button" class="list-group-item list-group-item-action report-type-btn" 
                                data-type="researcher">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-users me-3 text-secondary"></i>
                                <div>
                                    <h6 class="mb-1">تقرير الباحثين</h6>
                                    <small class="text-muted">أداء الباحثين والمعاملات المسندة</small>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- إعدادات التقرير -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2 text-primary"></i>
                        إعدادات التقرير
                    </h5>
                </div>
                <div class="card-body">
                    <form id="reportForm" method="POST" action="{{ url_for('generate_report') }}">
                        <input type="hidden" id="reportType" name="report_type" value="transactions">
                        
                        <!-- فلاتر التقرير -->
                        <div id="reportFilters">
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-filter me-1"></i>
                                        فلاتر التقرير
                                    </h6>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="dateFrom" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="dateFrom" name="date_from">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="dateTo" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="dateTo" name="date_to">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="statusFilter" class="form-label">الحالة</label>
                                    <select class="form-select" id="statusFilter" name="status_id">
                                        <option value="">جميع الحالات</option>
                                        <option value="1">جديد</option>
                                        <option value="2">قيد المعالجة</option>
                                        <option value="3">في انتظار الرد</option>
                                        <option value="4">مكتمل</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="researcherFilter" class="form-label">الباحث</label>
                                    <select class="form-select" id="researcherFilter" name="researcher_id">
                                        <option value="">جميع الباحثين</option>
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- تنسيق التقرير -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-file-export me-1"></i>
                                    تنسيق التقرير
                                </h6>
                            </div>
                            
                            <div class="col-12">
                                <div class="row">
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="format" id="formatExcel" value="excel" checked>
                                            <label class="form-check-label" for="formatExcel">
                                                <i class="fas fa-file-excel text-success me-2"></i>
                                                Excel (.xlsx)
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="format" id="formatPDF" value="pdf">
                                            <label class="form-check-label" for="formatPDF">
                                                <i class="fas fa-file-pdf text-danger me-2"></i>
                                                PDF (.pdf)
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="format" id="formatCSV" value="csv">
                                            <label class="form-check-label" for="formatCSV">
                                                <i class="fas fa-file-csv text-info me-2"></i>
                                                CSV (.csv)
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- أزرار الإجراءات -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-outline-primary" id="previewBtn">
                                        <i class="fas fa-eye me-2"></i>
                                        معاينة
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-download me-2"></i>
                                        تحميل التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- معاينة التقرير -->
            <div class="card border-0 shadow-sm mt-4" id="previewCard" style="display: none;">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">
                        <i class="fas fa-eye me-2 text-info"></i>
                        معاينة التقرير
                    </h5>
                </div>
                <div class="card-body">
                    <div id="previewContent">
                        <!-- سيتم تحميل المحتوى هنا -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .report-type-btn {
        border: none;
        border-radius: 8px;
        margin-bottom: 8px;
        transition: all 0.3s ease;
    }
    
    .report-type-btn:hover {
        background-color: rgba(0,123,255,0.1);
        transform: translateX(-5px);
    }
    
    .report-type-btn.active {
        background-color: rgba(0,123,255,0.1);
        border-left: 4px solid #007bff;
    }
    
    .form-check-input:checked {
        background-color: #007bff;
        border-color: #007bff;
    }
    
    .preview-table {
        font-size: 0.875rem;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: 700;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تبديل نوع التقرير
    const reportTypeBtns = document.querySelectorAll('.report-type-btn');
    const reportTypeInput = document.getElementById('reportType');
    const reportFilters = document.getElementById('reportFilters');
    
    reportTypeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // إزالة الفئة النشطة من جميع الأزرار
            reportTypeBtns.forEach(b => b.classList.remove('active'));
            
            // إضافة الفئة النشطة للزر المحدد
            this.classList.add('active');
            
            // تحديث نوع التقرير
            const reportType = this.dataset.type;
            reportTypeInput.value = reportType;
            
            // إظهار/إخفاء الفلاتر حسب نوع التقرير
            if (reportType === 'statistics') {
                reportFilters.style.display = 'none';
            } else {
                reportFilters.style.display = 'block';
            }
            
            // إخفاء المعاينة
            document.getElementById('previewCard').style.display = 'none';
        });
    });
    
    // معاينة التقرير
    document.getElementById('previewBtn').addEventListener('click', function() {
        const reportType = reportTypeInput.value;
        
        Utils.showAlert('جاري تحميل المعاينة...', 'info', 2000);
        
        fetch(`/api/reports/preview?type=${reportType}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showPreview(reportType, data.data);
                } else {
                    Utils.showAlert('خطأ في تحميل المعاينة: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                Utils.showAlert('خطأ في الاتصال بالخادم', 'danger');
                console.error('Preview error:', error);
            });
    });
    
    // إرسال النموذج
    document.getElementById('reportForm').addEventListener('submit', function(e) {
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        Utils.showLoading(submitBtn, 'جاري إنشاء التقرير...');
        
        // إعادة النص الأصلي بعد 10 ثوان
        setTimeout(() => {
            Utils.hideLoading(submitBtn, originalText);
        }, 10000);
    });
});

function showPreview(reportType, data) {
    const previewCard = document.getElementById('previewCard');
    const previewContent = document.getElementById('previewContent');
    
    if (reportType === 'statistics') {
        // عرض الإحصائيات
        let html = '<div class="row">';
        
        // إجمالي المعاملات
        html += `
            <div class="col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-number">${data.total}</div>
                    <div>إجمالي المعاملات</div>
                </div>
            </div>
        `;
        
        // إحصائيات حسب الحالة
        html += '<div class="col-12"><h6>إحصائيات حسب الحالة:</h6><div class="row">';
        for (const [status, count] of Object.entries(data.by_status)) {
            html += `
                <div class="col-md-3 mb-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">${count}</h5>
                            <p class="card-text">${status}</p>
                        </div>
                    </div>
                </div>
            `;
        }
        html += '</div></div>';
        
        // إحصائيات حسب الأولوية
        html += '<div class="col-12 mt-3"><h6>إحصائيات حسب الأولوية:</h6><div class="row">';
        for (const [priority, count] of Object.entries(data.by_priority)) {
            html += `
                <div class="col-md-3 mb-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">${count}</h5>
                            <p class="card-text">${priority}</p>
                        </div>
                    </div>
                </div>
            `;
        }
        html += '</div></div></div>';
        
        previewContent.innerHTML = html;
        
    } else {
        // عرض جدول المعاملات
        let html = `
            <div class="table-responsive">
                <table class="table table-striped preview-table">
                    <thead>
                        <tr>
                            <th>رقم الوارد</th>
                            <th>التاريخ</th>
                            <th>الموضوع</th>
                            <th>الباحث</th>
                            <th>الحالة</th>
                            <th>الأولوية</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        data.forEach(transaction => {
            html += `
                <tr>
                    <td>${transaction.head_incoming_no}</td>
                    <td>${transaction.head_incoming_date}</td>
                    <td>${transaction.subject.length > 50 ? transaction.subject.substring(0, 50) + '...' : transaction.subject}</td>
                    <td>${transaction.researcher}</td>
                    <td><span class="badge bg-primary">${transaction.status}</span></td>
                    <td><span class="badge bg-info">${transaction.priority}</span></td>
                </tr>
            `;
        });
        
        html += `
                    </tbody>
                </table>
            </div>
            <div class="text-center mt-3">
                <small class="text-muted">معاينة أول 10 سجلات من إجمالي ${data.total || 'غير محدد'} معاملة</small>
            </div>
        `;
        
        previewContent.innerHTML = html;
    }
    
    previewCard.style.display = 'block';
    previewCard.scrollIntoView({ behavior: 'smooth' });
}
</script>
{% endblock %}
