{% extends "base.html" %}

{% block title %}
{% if transaction %}تعديل المعاملة{% else %}معاملة جديدة{% endif %} - نظام متابعة الوارد والصادر
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-{% if transaction %}edit{% else %}plus{% endif %} me-2 text-primary"></i>
                        {% if transaction %}تعديل المعاملة{% else %}معاملة جديدة{% endif %}
                    </h2>
                    <p class="text-muted mb-0">
                        {% if transaction %}
                        تعديل بيانات المعاملة رقم {{ transaction.head_incoming_no }}
                        {% else %}
                        إنشاء معاملة جديدة في النظام
                        {% endif %}
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('transactions') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- النموذج -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">
                        <i class="fas fa-form me-2 text-success"></i>
                        بيانات المعاملة
                    </h5>
                </div>
                
                <div class="card-body">
                    <form method="POST" id="transactionForm" data-validate="true" novalidate>
                        <div class="row">
                            <!-- البيانات الأساسية -->
                            <div class="col-12">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-info-circle me-1"></i>
                                    البيانات الأساسية
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="head_incoming_no" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>
                                    رقم وارد رئيس المصلحة <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="head_incoming_no" 
                                       name="head_incoming_no" 
                                       value="{{ transaction.head_incoming_no if transaction else '' }}"
                                       required 
                                       placeholder="مثال: 2024/001">
                                <div class="form-text">رقم الوارد الرسمي من رئيس المصلحة</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="head_incoming_date" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>
                                    تاريخ وارد رئيس المصلحة <span class="text-danger">*</span>
                                </label>
                                <input type="date" 
                                       class="form-control" 
                                       id="head_incoming_date" 
                                       name="head_incoming_date" 
                                       value="{{ transaction.head_incoming_date|date if transaction else '' }}"
                                       required>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="subject" class="form-label">
                                    <i class="fas fa-file-alt me-1"></i>
                                    الموضوع <span class="text-danger">*</span>
                                </label>
                                <textarea class="form-control" 
                                          id="subject" 
                                          name="subject" 
                                          rows="3" 
                                          required 
                                          placeholder="اكتب موضوع المعاملة بالتفصيل">{{ transaction.subject if transaction else '' }}</textarea>
                            </div>
                            
                            <!-- تفاصيل إضافية -->
                            <div class="col-12 mt-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-cogs me-1"></i>
                                    التفاصيل والتصنيف
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="visa_type_id" class="form-label">
                                    <i class="fas fa-passport me-1"></i>
                                    نوع التأشيرة
                                </label>
                                <select class="form-select" id="visa_type_id" name="visa_type_id">
                                    <option value="">اختر نوع التأشيرة</option>
                                    {% for visa_type in visa_types %}
                                    <option value="{{ visa_type.id }}" 
                                            {% if transaction and transaction.visa_type_id == visa_type.id %}selected{% endif %}>
                                        {{ visa_type.visa_type }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="received_from_id" class="form-label">
                                    <i class="fas fa-building me-1"></i>
                                    وارد من
                                </label>
                                <select class="form-select" id="received_from_id" name="received_from_id">
                                    <option value="">اختر مصدر الورود</option>
                                    {% for source in sources %}
                                    <option value="{{ source.id }}" 
                                            {% if transaction and transaction.received_from_id == source.id %}selected{% endif %}>
                                        {{ source.received_from }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="priority" class="form-label">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    الأولوية
                                </label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="low" {% if transaction and transaction.priority == 'low' %}selected{% endif %}>منخفضة</option>
                                    <option value="medium" {% if not transaction or transaction.priority == 'medium' %}selected{% endif %}>متوسطة</option>
                                    <option value="high" {% if transaction and transaction.priority == 'high' %}selected{% endif %}>مرتفعة</option>
                                    <option value="urgent" {% if transaction and transaction.priority == 'urgent' %}selected{% endif %}>عاجلة</option>
                                </select>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="due_date" class="form-label">
                                    <i class="fas fa-clock me-1"></i>
                                    تاريخ الاستحقاق
                                </label>
                                <input type="date" 
                                       class="form-control" 
                                       id="due_date" 
                                       name="due_date" 
                                       value="{{ transaction.due_date|date if transaction and transaction.due_date else '' }}">
                                <div class="form-text">تاريخ مطلوب إنجاز المعاملة</div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="request_status_id" class="form-label">
                                    <i class="fas fa-flag me-1"></i>
                                    حالة الطلب
                                </label>
                                <select class="form-select" id="request_status_id" name="request_status_id">
                                    {% for status in statuses %}
                                    <option value="{{ status.id }}" 
                                            {% if not transaction and loop.first %}selected{% endif %}
                                            {% if transaction and transaction.request_status_id == status.id %}selected{% endif %}>
                                        {{ status.request_status }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <!-- إسناد الباحثين -->
                            <div class="col-12 mt-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-users me-1"></i>
                                    إسناد الباحثين
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="researcher_1_id" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    الباحث الأول
                                </label>
                                <select class="form-select" id="researcher_1_id" name="researcher_1_id">
                                    <option value="">اختر الباحث الأول</option>
                                    {% for user in users %}
                                    {% if user.permission == 'user' or user.permission == 'admin' %}
                                    <option value="{{ user.user_id }}" 
                                            {% if transaction and transaction.researcher_1_id == user.user_id %}selected{% endif %}>
                                        {{ user.full_name }}
                                    </option>
                                    {% endif %}
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="researcher_2_id" class="form-label">
                                    <i class="fas fa-user-plus me-1"></i>
                                    الباحث الثاني (اختياري)
                                </label>
                                <select class="form-select" id="researcher_2_id" name="researcher_2_id">
                                    <option value="">اختر الباحث الثاني</option>
                                    {% for user in users %}
                                    {% if user.permission == 'user' or user.permission == 'admin' %}
                                    <option value="{{ user.user_id }}" 
                                            {% if transaction and transaction.researcher_2_id == user.user_id %}selected{% endif %}>
                                        {{ user.full_name }}
                                    </option>
                                    {% endif %}
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="action_taken_id" class="form-label">
                                    <i class="fas fa-tasks me-1"></i>
                                    الإجراء المتخذ
                                </label>
                                <select class="form-select" id="action_taken_id" name="action_taken_id">
                                    <option value="">اختر الإجراء المتخذ</option>
                                    {% for action in actions %}
                                    <option value="{{ action.id }}" 
                                            {% if transaction and transaction.action_taken_id == action.id %}selected{% endif %}>
                                        {{ action.action_taken }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <!-- ملاحظات الباحث -->
                            <div class="col-12 mt-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-sticky-note me-1"></i>
                                    ملاحظات إضافية
                                </h6>
                            </div>
                            
                            <div class="col-12 mb-4">
                                <label for="researcher_notes" class="form-label">
                                    <i class="fas fa-comment me-1"></i>
                                    ملاحظات الباحث
                                </label>
                                <textarea class="form-control" 
                                          id="researcher_notes" 
                                          name="researcher_notes" 
                                          rows="4" 
                                          placeholder="اكتب أي ملاحظات أو تعليقات حول المعاملة">{{ transaction.researcher_notes if transaction else '' }}</textarea>
                            </div>
                        </div>
                        
                        <!-- أزرار الحفظ -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ url_for('transactions') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        {% if transaction %}تحديث المعاملة{% else %}حفظ المعاملة{% endif %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .form-label {
        font-weight: 600;
        color: #495057;
    }
    
    .form-label .fas {
        color: #6c757d;
        width: 16px;
    }
    
    .text-danger {
        font-weight: 600;
    }
    
    .form-text {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .card-header h6 {
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .form-control:focus,
    .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        font-weight: 600;
    }
    
    .btn-primary:hover {
        background: linear-gradient(45deg, #5a6fd8, #6a4190);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('transactionForm');
    
    // التحقق من صحة النموذج عند الإرسال
    form.addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            Utils.showAlert('يرجى تصحيح الأخطاء في النموذج', 'danger');
            return false;
        }
        
        // إظهار مؤشر التحميل
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        Utils.showLoading(submitBtn, 'جاري الحفظ...');
    });
    
    // التحقق من تطابق الباحثين
    const researcher1 = document.getElementById('researcher_1_id');
    const researcher2 = document.getElementById('researcher_2_id');
    
    function checkResearcherConflict() {
        if (researcher1.value && researcher2.value && researcher1.value === researcher2.value) {
            Utils.showAlert('لا يمكن أن يكون الباحث الأول والثاني نفس الشخص', 'warning');
            researcher2.value = '';
        }
    }
    
    researcher1.addEventListener('change', checkResearcherConflict);
    researcher2.addEventListener('change', checkResearcherConflict);
    
    // تحديد تاريخ الاستحقاق تلقائياً حسب الأولوية
    const prioritySelect = document.getElementById('priority');
    const dueDateInput = document.getElementById('due_date');
    
    prioritySelect.addEventListener('change', function() {
        if (!dueDateInput.value) {
            const today = new Date();
            let daysToAdd = 30; // افتراضي
            
            switch (this.value) {
                case 'urgent':
                    daysToAdd = 3;
                    break;
                case 'high':
                    daysToAdd = 7;
                    break;
                case 'medium':
                    daysToAdd = 14;
                    break;
                case 'low':
                    daysToAdd = 30;
                    break;
            }
            
            const dueDate = new Date(today.getTime() + (daysToAdd * 24 * 60 * 60 * 1000));
            dueDateInput.value = dueDate.toISOString().split('T')[0];
        }
    });
});

function validateForm() {
    let isValid = true;
    
    // التحقق من الحقول المطلوبة
    const requiredFields = [
        { id: 'head_incoming_no', name: 'رقم وارد رئيس المصلحة' },
        { id: 'head_incoming_date', name: 'تاريخ وارد رئيس المصلحة' },
        { id: 'subject', name: 'الموضوع' }
    ];
    
    requiredFields.forEach(field => {
        const element = document.getElementById(field.id);
        if (!element.value.trim()) {
            showFieldError(element, `${field.name} مطلوب`);
            isValid = false;
        } else {
            clearFieldError(element);
        }
    });
    
    // التحقق من تنسيق رقم الوارد
    const incomingNo = document.getElementById('head_incoming_no');
    const incomingNoPattern = /^\d{4}\/\d+$/;
    if (incomingNo.value && !incomingNoPattern.test(incomingNo.value)) {
        showFieldError(incomingNo, 'تنسيق رقم الوارد غير صحيح (مثال: 2024/001)');
        isValid = false;
    }
    
    // التحقق من التاريخ
    const incomingDate = document.getElementById('head_incoming_date');
    if (incomingDate.value) {
        const selectedDate = new Date(incomingDate.value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (selectedDate > today) {
            showFieldError(incomingDate, 'لا يمكن أن يكون تاريخ الوارد في المستقبل');
            isValid = false;
        }
    }
    
    // التحقق من تاريخ الاستحقاق
    const dueDate = document.getElementById('due_date');
    if (dueDate.value) {
        const selectedDueDate = new Date(dueDate.value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (selectedDueDate < today) {
            showFieldError(dueDate, 'تاريخ الاستحقاق لا يمكن أن يكون في الماضي');
            isValid = false;
        }
    }
    
    return isValid;
}

function showFieldError(field, message) {
    field.classList.add('is-invalid');
    
    let errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        field.parentNode.appendChild(errorDiv);
    }
    
    errorDiv.textContent = message;
}

function clearFieldError(field) {
    field.classList.remove('is-invalid');
    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}
</script>
{% endblock %}
