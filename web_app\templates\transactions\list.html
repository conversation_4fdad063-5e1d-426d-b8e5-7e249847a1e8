{% extends "base.html" %}

{% block title %}المعاملات - نظام متابعة الوارد والصادر{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-list me-2 text-primary"></i>
                        المعاملات
                    </h2>
                    <p class="text-muted mb-0">إدارة ومتابعة جميع المعاملات</p>
                </div>
                <div>
                    <a href="{{ url_for('new_transaction') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        معاملة جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مربع البحث والفلاتر -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-white border-0 py-3">
            <h5 class="mb-0">
                <i class="fas fa-search me-2 text-info"></i>
                البحث والفلاتر
            </h5>
        </div>
        <div class="card-body">
            <form id="searchForm" class="row g-3">
                <div class="col-md-3">
                    <label for="searchNumber" class="form-label">رقم الوارد</label>
                    <input type="text" class="form-control" id="searchNumber" name="head_incoming_no" placeholder="ابحث برقم الوارد">
                </div>
                
                <div class="col-md-3">
                    <label for="searchSubject" class="form-label">الموضوع</label>
                    <input type="text" class="form-control" id="searchSubject" name="subject" placeholder="ابحث في الموضوع">
                </div>
                
                <div class="col-md-2">
                    <label for="dateFrom" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="dateFrom" name="date_from">
                </div>
                
                <div class="col-md-2">
                    <label for="dateTo" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="dateTo" name="date_to">
                </div>
                
                <div class="col-md-2">
                    <label for="statusFilter" class="form-label">الحالة</label>
                    <select class="form-select" id="statusFilter" name="status_id">
                        <option value="">جميع الحالات</option>
                        {% for status in statuses %}
                        <option value="{{ status.id }}">{{ status.request_status }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-12">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="clearSearch">
                        <i class="fas fa-times me-1"></i>
                        مسح
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- جدول المعاملات -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-0 py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2 text-success"></i>
                    قائمة المعاملات
                    <span class="badge bg-primary ms-2" id="transactionCount">{{ transactions|length }}</span>
                </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="exportExcel">
                        <i class="fas fa-file-excel me-1"></i>
                        Excel
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" id="exportPDF">
                        <i class="fas fa-file-pdf me-1"></i>
                        PDF
                    </button>
                </div>
            </div>
        </div>
        
        <div class="card-body p-0">
            {% if transactions %}
            <div class="table-responsive">
                <table class="table table-hover mb-0 data-table" id="transactionsTable" 
                       data-searchable="true" data-paginated="true" data-items-per-page="10">
                    <thead class="table-light">
                        <tr>
                            <th data-sortable>رقم الوارد</th>
                            <th data-sortable>التاريخ</th>
                            <th>الموضوع</th>
                            <th data-sortable>الباحث الأول</th>
                            <th data-sortable>نوع التأشيرة</th>
                            <th data-sortable>الحالة</th>
                            <th data-sortable>الأولوية</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="transactionsTableBody">
                        {% for transaction in transactions %}
                        <tr data-transaction-id="{{ transaction.id }}">
                            <td>
                                <strong class="text-primary">{{ transaction.head_incoming_no }}</strong>
                            </td>
                            <td>
                                <small class="text-muted">{{ transaction.head_incoming_date|date }}</small>
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 300px;" title="{{ transaction.subject }}">
                                    {{ transaction.subject }}
                                </div>
                            </td>
                            <td>
                                {% if transaction.researcher_1_id %}
                                <span class="badge bg-info">باحث مسند</span>
                                {% else %}
                                <span class="badge bg-secondary">غير مسند</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if transaction.visa_type_id %}
                                <span class="badge bg-light text-dark">نوع محدد</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if transaction.request_status_id == 1 %}
                                <span class="badge status-new">جديد</span>
                                {% elif transaction.request_status_id == 2 %}
                                <span class="badge status-in-progress">قيد المعالجة</span>
                                {% elif transaction.request_status_id == 4 %}
                                <span class="badge status-completed">مكتمل</span>
                                {% else %}
                                <span class="badge status-pending">معلق</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if transaction.priority == 'urgent' %}
                                <span class="badge bg-danger">عاجل</span>
                                {% elif transaction.priority == 'high' %}
                                <span class="badge bg-warning">مرتفع</span>
                                {% elif transaction.priority == 'medium' %}
                                <span class="badge bg-info">متوسط</span>
                                {% else %}
                                <span class="badge bg-secondary">منخفض</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-primary" 
                                            onclick="viewTransaction({{ transaction.id }})"
                                            title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-success" 
                                            onclick="editTransaction({{ transaction.id }})"
                                            title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    {% if current_user.is_admin() %}
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="deleteTransaction({{ transaction.id }})"
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- الترقيم -->
            <div class="card-footer bg-white border-0">
                <nav aria-label="ترقيم المعاملات">
                    <ul class="pagination pagination-sm justify-content-center mb-0" id="pagination">
                        <!-- سيتم إنشاؤها بواسطة JavaScript -->
                    </ul>
                </nav>
            </div>
            
            {% else %}
            <!-- حالة عدم وجود معاملات -->
            <div class="empty-state">
                <i class="fas fa-inbox"></i>
                <h4>لا توجد معاملات</h4>
                <p class="text-muted">لم يتم العثور على أي معاملات. ابدأ بإنشاء معاملة جديدة.</p>
                <a href="{{ url_for('new_transaction') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء معاملة جديدة
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- نافذة عرض التفاصيل -->
<div class="modal fade" id="transactionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-alt me-2"></i>
                    تفاصيل المعاملة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="transactionDetails">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="editFromModal">تعديل</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة البحث
    const searchForm = document.getElementById('searchForm');
    const clearSearchBtn = document.getElementById('clearSearch');
    
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        performSearch();
    });
    
    clearSearchBtn.addEventListener('click', function() {
        searchForm.reset();
        loadAllTransactions();
    });
    
    // تهيئة أزرار التصدير
    document.getElementById('exportExcel').addEventListener('click', function() {
        exportToExcel();
    });
    
    document.getElementById('exportPDF').addEventListener('click', function() {
        exportToPDF();
    });
});

// دالة البحث
async function performSearch() {
    const formData = new FormData(document.getElementById('searchForm'));
    const searchParams = Object.fromEntries(formData.entries());
    
    // إزالة القيم الفارغة
    Object.keys(searchParams).forEach(key => {
        if (!searchParams[key]) {
            delete searchParams[key];
        }
    });
    
    try {
        Utils.showAlert('جاري البحث...', 'info', 2000);
        
        const response = await API.searchTransactions(searchParams);
        
        if (response.success) {
            updateTransactionsTable(response.transactions);
            document.getElementById('transactionCount').textContent = response.count;
            Utils.showAlert(`تم العثور على ${response.count} معاملة`, 'success');
        } else {
            Utils.showAlert('خطأ في البحث: ' + response.error, 'danger');
        }
    } catch (error) {
        Utils.showAlert('خطأ في الاتصال بالخادم', 'danger');
        console.error('Search error:', error);
    }
}

// تحديث جدول المعاملات
function updateTransactionsTable(transactions) {
    const tbody = document.getElementById('transactionsTableBody');
    
    if (transactions.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <i class="fas fa-search fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">لم يتم العثور على نتائج</p>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = transactions.map(transaction => `
        <tr data-transaction-id="${transaction.id}">
            <td><strong class="text-primary">${transaction.head_incoming_no}</strong></td>
            <td><small class="text-muted">${Utils.formatDate(transaction.head_incoming_date)}</small></td>
            <td>
                <div class="text-truncate" style="max-width: 300px;" title="${transaction.subject}">
                    ${transaction.subject}
                </div>
            </td>
            <td>
                ${transaction.researcher_1_id ? 
                    '<span class="badge bg-info">باحث مسند</span>' : 
                    '<span class="badge bg-secondary">غير مسند</span>'
                }
            </td>
            <td>
                ${transaction.visa_type_id ? 
                    '<span class="badge bg-light text-dark">نوع محدد</span>' : 
                    '<span class="text-muted">-</span>'
                }
            </td>
            <td>${getStatusBadge(transaction.request_status_id)}</td>
            <td>${getPriorityBadge(transaction.priority)}</td>
            <td>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary" 
                            onclick="viewTransaction(${transaction.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" class="btn btn-outline-success" 
                            onclick="editTransaction(${transaction.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    ${getCurrentUser().is_admin ? `
                    <button type="button" class="btn btn-outline-danger" 
                            onclick="deleteTransaction(${transaction.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');
}

// دوال مساعدة للحالات والأولويات
function getStatusBadge(statusId) {
    const statuses = {
        1: '<span class="badge status-new">جديد</span>',
        2: '<span class="badge status-in-progress">قيد المعالجة</span>',
        4: '<span class="badge status-completed">مكتمل</span>'
    };
    return statuses[statusId] || '<span class="badge status-pending">معلق</span>';
}

function getPriorityBadge(priority) {
    const priorities = {
        'urgent': '<span class="badge bg-danger">عاجل</span>',
        'high': '<span class="badge bg-warning">مرتفع</span>',
        'medium': '<span class="badge bg-info">متوسط</span>',
        'low': '<span class="badge bg-secondary">منخفض</span>'
    };
    return priorities[priority] || '<span class="badge bg-secondary">منخفض</span>';
}

// دوال الإجراءات
function viewTransaction(id) {
    // فتح نافذة عرض التفاصيل
    const modal = new bootstrap.Modal(document.getElementById('transactionModal'));
    
    // تحميل التفاصيل (يمكن تطويرها لاحقاً)
    document.getElementById('transactionDetails').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري تحميل التفاصيل...</p>
        </div>
    `;
    
    modal.show();
    
    // محاكاة تحميل البيانات
    setTimeout(() => {
        document.getElementById('transactionDetails').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <strong>رقم الوارد:</strong> 2024/001<br>
                    <strong>التاريخ:</strong> 2024-01-15<br>
                    <strong>الموضوع:</strong> طلب تأشيرة سياحية
                </div>
                <div class="col-md-6">
                    <strong>الحالة:</strong> <span class="badge status-new">جديد</span><br>
                    <strong>الأولوية:</strong> <span class="badge bg-info">متوسط</span><br>
                    <strong>الباحث:</strong> غير مسند
                </div>
            </div>
            <hr>
            <div>
                <strong>ملاحظات الباحث:</strong><br>
                <p class="text-muted">لا توجد ملاحظات حتى الآن.</p>
            </div>
        `;
    }, 1000);
}

function editTransaction(id) {
    window.location.href = `/transactions/${id}/edit`;
}

function deleteTransaction(id) {
    if (Utils.confirmDelete('هل أنت متأكد من حذف هذه المعاملة؟ لا يمكن التراجع عن هذا الإجراء.')) {
        // تنفيذ الحذف (يمكن تطويرها لاحقاً)
        Utils.showAlert('تم حذف المعاملة بنجاح', 'success');
        
        // إزالة الصف من الجدول
        const row = document.querySelector(`tr[data-transaction-id="${id}"]`);
        if (row) {
            row.remove();
        }
    }
}

function loadAllTransactions() {
    window.location.reload();
}

function exportToExcel() {
    Utils.showAlert('جاري تصدير البيانات إلى Excel...', 'info');
    // تنفيذ التصدير (يمكن تطويرها لاحقاً)
}

function exportToPDF() {
    Utils.showAlert('جاري تصدير البيانات إلى PDF...', 'info');
    // تنفيذ التصدير (يمكن تطويرها لاحقاً)
}

function getCurrentUser() {
    // إرجاع بيانات المستخدم الحالي (يمكن تطويرها لاحقاً)
    return {
        is_admin: {{ 'true' if current_user.is_admin() else 'false' }}
    };
}
</script>
{% endblock %}
