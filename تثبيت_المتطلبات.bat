@echo off
:: تعيين ترميز UTF-8 للـ console
chcp 65001 > nul

:: تعيين متغيرات البيئة للترميز
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1

:: تعيين عنوان النافذة
title IOTS - Install Requirements

:: مسح الشاشة
cls

echo.
echo ========================================
echo   IOTS - Installing Requirements
echo   تثبيت متطلبات النظام
echo ========================================
echo.
echo Installing Python packages...
echo جاري تثبيت مكتبات Python...
echo.

:: تحديث pip أولاً
echo Updating pip...
echo تحديث pip...
python -m pip install --upgrade pip

echo.
echo Installing requirements from requirements.txt...
echo تثبيت المتطلبات من ملف requirements.txt...
echo.

:: تثبيت المتطلبات
pip install -r requirements.txt

echo.
echo Installation completed!
echo انتهى التثبيت!
echo.

:: اختبار التثبيت
echo Testing installation...
echo اختبار التثبيت...
python -c "import flask, flask_login, PyQt6, mysql.connector; print('✓ All main packages installed successfully')"

echo.
pause
