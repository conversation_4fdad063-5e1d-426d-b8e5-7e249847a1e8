@echo off
:: تعيين ترميز UTF-8 للـ console
chcp 65001 > nul

:: تعيين متغيرات البيئة للترميز
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1

:: تعيين عنوان النافذة
title IOTS - Install Requirements

:: مسح الشاشة
cls

echo.
echo ========================================
echo   IOTS - Installing Requirements
echo   تثبيت متطلبات النظام
echo ========================================
echo.
echo Installing Python packages...
echo جاري تثبيت مكتبات Python...
echo.

:: تحديث pip أولاً
echo Updating pip...
echo تحديث pip...
python -m pip install --upgrade pip

echo.
echo Installing requirements from requirements.txt...
echo تثبيت المتطلبات من ملف requirements.txt...
echo.

:: تثبيت المتطلبات الأساسية واحدة تلو الأخرى
echo Installing Flask...
pip install Flask

echo Installing Flask-Login...
pip install Flask-Login

echo Installing Werkzeug...
pip install Werkzeug

echo Installing MySQL connector...
pip install mysql-connector-python

echo Installing PyQt6...
pip install PyQt6

echo Installing openpyxl...
pip install openpyxl

echo Installing reportlab...
pip install reportlab

echo Installing bcrypt...
pip install bcrypt

echo Installing requests...
pip install requests

echo.
echo Installation completed!
echo انتهى التثبيت!
echo.

:: اختبار التثبيت
echo Testing installation...
echo اختبار التثبيت...
python -c "import flask, flask_login, mysql.connector; print('✓ Core packages installed successfully')"

echo.
pause
