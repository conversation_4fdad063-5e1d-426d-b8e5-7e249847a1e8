@echo off
:: تعيين ترميز UTF-8 للـ console
chcp 65001 > nul

:: تعيين متغيرات البيئة للترميز
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1

:: تعيين عنوان النافذة
title IOTS - Quick Start

:: مسح الشاشة
cls

echo.
echo ========================================
echo   IOTS - Quick Start
echo   تشغيل سريع للنظام
echo ========================================
echo.

:: التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo ❌ Python is not installed or not in PATH
    pause
    exit /b 1
)

:: التحقق من وجود Flask
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo ⚠ Flask غير مثبت - جاري التثبيت...
    echo ⚠ Flask not installed - Installing...
    pip install Flask Flask-Login
)

:: التحقق من وجود الملفات الثابتة
if not exist "web_app\static\css\bootstrap.rtl.min.css" (
    echo ⚠ الملفات الثابتة مفقودة - جاري التحميل...
    echo ⚠ Static files missing - Downloading...
    python download_assets.py
)

echo.
echo ✅ جميع المتطلبات جاهزة
echo ✅ All requirements ready
echo.
echo 🚀 جاري تشغيل التطبيق...
echo 🚀 Starting application...
echo.
echo 🌐 الوصول: http://localhost:5000
echo 🌐 Access: http://localhost:5000
echo.
echo 👤 تسجيل الدخول:
echo 👤 Login:
echo    المستخدم/Username: admin
echo    كلمة المرور/Password: admin123
echo.
echo ⏹ للإيقاف اضغط Ctrl+C
echo ⏹ Press Ctrl+C to stop
echo.

:: تشغيل التطبيق
python main.py

echo.
echo 📴 تم إيقاف التطبيق
echo 📴 Application stopped
echo.
pause
