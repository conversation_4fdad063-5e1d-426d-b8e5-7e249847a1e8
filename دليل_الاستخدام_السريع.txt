
# دليل الاستخدام السريع - نظام متابعة الوارد والصادر

## بيانات تسجيل الدخول الافتراضية:

### مدير النظام:
- اسم المستخدم: admin
- كلمة المرور: admin123

### مستخدم عادي:
- اسم المستخدم: user1  
- كلمة المرور: user123

## طرق التشغيل:

### 1. تشغيل تطبيق الويب:
- انقر مرتين على: تشغيل_تطبيق_الويب.bat
- أو استخدم الأمر: python main.py
- افتح المتصفح على: http://localhost:5000

### 2. تشغيل تطبيق سطح المكتب:
- انقر مرتين على: تشغيل_تطبيق_سطح_المكتب.bat
- أو استخدم الأمر: python desktop_app/main.py

### 3. إعد<PERSON> قاعدة البيانات:
- انقر مرتين على: إعداد_قاعدة_البيانات.bat
- أو استخدم الأمر: python -c "from config.database import setup_database; setup_database()"

## الميزات الرئيسية:

### تطبيق الويب:
- لوحة تحكم تفاعلية
- إدارة المعاملات (إنشاء، تعديل، حذف)
- بحث وفلترة متقدمة
- نظام صلاحيات
- واجهة عربية متجاوبة

### تطبيق سطح المكتب:
- واجهة احترافية RTL
- نمط تشغيل مزدوج (متصل/غير متصل)
- مزامنة ذكية للبيانات
- إشعارات سطح المكتب

## إعدادات قاعدة البيانات:

يمكنك تعديل إعدادات قاعدة البيانات في ملف:
config/configuration.ini

## الدعم الفني:

للحصول على المساعدة:
- راجع ملف README.md للتوثيق الكامل
- تحقق من ملفات السجل في حالة وجود أخطاء

## ملاحظات مهمة:

1. تأكد من تشغيل خادم MySQL قبل استخدام النظام
2. يمكن استخدام تطبيق سطح المكتب بدون اتصال بالإنترنت
3. يتم حفظ البيانات المحلية في ملف: db/local.db
4. للمزامنة بين التطبيقين، استخدم زر "مزامنة" في تطبيق سطح المكتب

تاريخ الإعداد: 2025-08-04 21:25
الإصدار: 1.0.0
